<h1 align="center">create-vue-chrome-ext</h1>

<p align="center">
  <strong>Chrome 扩展快速开发框架</strong>
</p>

<p align="center">
  <a href="#项目结构">项目结构</a> •
  <a href="#功能特性">功能特性</a> •
  <a href="#开发指南">开发指南</a> •
  <a href="#架构说明">架构说明</a>
</p>

## 📁 项目结构

```
vue-extv3/
├── 📄 LICENSE
├── 📄 package-lock.json
├── 📄 package.json
├── 📄 README.en.md
├── 📄 README.md
├── 📄 tsconfig.json
├── 📄 webpack.config.js
│
├── 📁 public/                    # 静态资源
│   ├── 📁 img/                   # 图标文件
│   │   ├── icon128.png
│   │   ├── icon16.png
│   │   ├── icon32.png
│   │   └── icon48.png
│   ├── 📄 index.html
│   └── 📁 js/
│       └── jquery-3.6.4.min.js
│
├── 📁 src/                       # 源代码
│   ├── 📄 manifest.json          # 扩展清单文件
│   ├── 📄 shims-vue.d.ts         # Vue类型声明
│   │
│   ├── 📁 _locales/              # 国际化文件
│   │   ├── 📁 en/
│   │   │   └── messages.json
│   │   └── 📁 zh_CN/
│   │       └── messages.json
│   │
│   ├── 📁 background/            # 后台脚本 (Service Worker)
│   │   ├── 📄 index.js           # 主后台脚本 - 消息监听、数据处理
│   │   └── 📄 automation.js      # 自动化操作模块 - API发送、页面自动化
│   │
│   ├── 📁 content/               # 内容脚本
│   │   ├── 📄 index.js           # 主内容脚本 - 注入拦截器、消息传递
│   │   ├── 📄 fileInterceptor.js # 网络请求拦截器 - 监控Amazon API
│   │   ├── 📄 HttpClient.js      # HTTP客户端工具
│   │   └── 📁 amazon/            # Amazon特定功能
│   │       └── 📄 Dome.vue
│   │
│   ├── 📁 utils/                 # 工具模块
│   │   ├── 📄 Message.js         # 消息传递工具
│   │   ├── 📄 Storage.js         # 存储管理工具 - 数据持久化
│   │   ├── 📄 apiClient.js       # API客户端 - 外部服务器通信
│   │   └── 📄 config.js          # 配置管理系统 - 扩展设置管理
│   │
│   └── 📁 pages/                 # 扩展页面
│       ├── 📁 components/
│       │   └── 📄 HelloWorld.vue
│       ├── 📁 options/           # 选项页面
│       │   ├── 📄 App.vue
│       │   └── 📄 main.js
│       └── 📁 popup/             # 弹出页面
│           ├── 📄 App.vue
│           ├── 📄 main.js
│           ├── 📁 assets/
│           │   └── logo.png
│           ├── 📁 router/
│           │   └── 📄 index.ts
│           └── 📁 views/
│               ├── 📄 AboutView.vue
│               └── 📄 HomeView.vue
│
└── 📁 dist/                      # 构建输出目录
    ├── 📄 manifest.json
    ├── 📁 js/                    # 编译后的JavaScript文件
    │   ├── background.js         # 后台脚本
    │   ├── content.js            # 内容脚本
    │   ├── fileInterceptor.js    # 拦截器脚本
    │   └── jquery-3.6.4.min.js
    ├── 📁 img/                   # 图标文件
    └── 📁 _locales/              # 国际化文件
```

## 🏗️ 架构说明

### 核心模块职责

#### 1. **Background Script (Service Worker)**
- **文件**: `src/background/index.js`
- **职责**: 
  - 监听来自 content script 的消息
  - 处理 Amazon API 响应数据
  - 协调自动化操作
  - 管理数据存储

#### 2. **Content Script**
- **文件**: `src/content/index.js`
- **职责**:
  - 注入网络请求拦截器
  - 监听页面消息
  - 向 background script 发送数据

#### 3. **网络拦截器**
- **文件**: `src/content/fileInterceptor.js`
- **职责**:
  - 拦截 Amazon API 请求
  - 提取响应数据
  - 通过 postMessage 发送到 content script

#### 4. **自动化模块**
- **文件**: `src/background/automation.js`
- **职责**:
  - 执行 API 数据发送
  - 页面自动化操作
  - 操作记录管理

#### 5. **存储管理**
- **文件**: `src/utils/storage.js`
- **职责**:
  - 数据持久化存储
  - 过期数据清理
  - 存储监听管理

#### 6. **API 客户端**
- **文件**: `src/utils/apiClient.js`
- **职责**:
  - 与外部服务器通信
  - API 配置管理
  - 错误处理和重试

#### 7. **配置管理**
- **文件**: `src/utils/config.js`
- **职责**:
  - 扩展配置管理
  - 配置验证和合并
  - 配置变化监听

### 数据流向

```
Amazon API → fileInterceptor.js → content/index.js → background/index.js → automation.js → 外部API
                                    ↓
                                 storage.js (数据持久化)
                                    ↓
                                 config.js (配置管理)
```

### 权限配置

```json
{
  "permissions": [
    "tabs",           // 标签页操作
    "scripting",      // 脚本注入
    "storage",        // 数据存储
    "activeTab"       // 活动标签页访问
  ],
  "host_permissions": [
    "*://*.amazon.co.uk/*",  // Amazon UK
    "*://*.amazon.com/*"     // Amazon US
  ]
}
```

## 🚀 功能特性

- ✅ **网络请求拦截**: 监控 Amazon API 调用
- ✅ **数据处理**: 自动处理 API 响应数据
- ✅ **自动化操作**: 页面自动化和 API 数据发送
- ✅ **数据持久化**: 本地存储和历史记录
- ✅ **配置管理**: 灵活的配置系统和验证
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **模块化架构**: 清晰的代码组织结构
- ✅ **国际化支持**: 多语言界面
- ✅ **Vue.js 集成**: 现代化的前端框架

## 🛠️ 开发指南

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 调试说明

1. **Content Script 日志**: 在页面控制台查看
2. **Background Script 日志**: 在 Service Worker 控制台查看
   - 打开 `chrome://extensions/`
   - 找到扩展并点击 "Service Worker" 链接
3. **网络拦截器日志**: 在页面控制台查看

## 📝 使用说明

1. 构建项目后，将 `dist` 目录加载为 Chrome 扩展
2. 访问 Amazon Seller Central 页面
3. 扩展会自动监控 API 调用并处理数据
4. 查看 Service Worker 控制台了解处理状态

## 🔧 配置说明

### API 配置
在 `src/utils/apiClient.js` 中配置你的 API 服务器地址：
```javascript
const API_CONFIG = {
  BASE_URL: 'https://your-api-server.com/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};
```

### 存储配置
在 `src/utils/storage.js` 中配置存储参数：
```javascript
const STORAGE_CONFIG = {
  DEFAULT_TTL: 24 * 60 * 60 * 1000, // 24小时
  MAX_ITEMS: 1000,
  CLEANUP_INTERVAL: 60 * 60 * 1000 // 1小时
};
```

### 扩展配置
通过配置管理系统管理扩展设置：
```javascript
// 获取配置
const apiEnabled = configManager.get('api.enabled', true);
const baseUrl = configManager.get('api.baseUrl', 'https://api.example.com');

// 设置配置
await configManager.set('api.baseUrl', 'https://new-api.example.com');
await configManager.set('automation.enabled', false);

// 验证配置
const validation = configManager.validate();
```

## �� 许可证

MIT License
