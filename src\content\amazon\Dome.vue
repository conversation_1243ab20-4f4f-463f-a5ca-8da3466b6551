<!--
 * @Author: Penk
 * @LastEditors: 郭然宁
 * @LastEditTime: 2023-07-24 17:43:20
 * @FilePath: \vue-chrome-ext\src\content\App\App.vue
-->
<template>
  <div class="penk_app">
    <h1>Hello {{msg}}</h1>
  </div>
</template>

<script>
  export default {
    name: 'app',
    components: {
    },
    data() {
      return {
        msg: '31content_script',
      }
    },
    mounted() {
      console.log(`Hello ${this.msg}...`);
    },
  }
</script>

<style>
  .penk_app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    margin-top: 60px;

    position: fixed;
    top: 0;
    right: 0;
    width: 200px;
    height: 100px;

    background-color: aqua;
  }
</style>