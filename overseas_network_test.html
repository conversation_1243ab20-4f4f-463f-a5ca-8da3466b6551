<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海外网络环境API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .network-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🌐 海外网络环境API测试工具</h1>
    
    <div class="network-info">
        <h3>🔍 网络环境检测</h3>
        <p><strong>检测到的环境:</strong> <span id="networkEnv">检测中...</span></p>
        <p><strong>建议超时设置:</strong> <span id="recommendedTimeout">计算中...</span></p>
        <p><strong>当前时区:</strong> <span id="timezone"></span></p>
    </div>
    
    <div class="container">
        <h2>⚙️ 测试配置</h2>
        <div class="form-group">
            <label for="timeoutSetting">超时设置 (秒):</label>
            <select id="timeoutSetting">
                <option value="30">30秒 (标准)</option>
                <option value="60" selected>60秒 (海外推荐)</option>
                <option value="90">90秒 (高延迟网络)</option>
                <option value="120">120秒 (极慢网络)</option>
            </select>
        </div>
        <div class="form-group">
            <label for="retryCount">重试次数:</label>
            <select id="retryCount">
                <option value="1">1次</option>
                <option value="3" selected>3次 (推荐)</option>
                <option value="5">5次</option>
            </select>
        </div>
    </div>

    <div class="container">
        <h2>🧪 API连接测试</h2>
        <button class="button" onclick="testApiWithConfig()">使用配置测试API</button>
        <button class="button" onclick="testMultipleTimeouts()">多超时测试</button>
        <button class="button" onclick="testNetworkLatency()">网络延迟测试</button>
        <div id="apiStatus"></div>
    </div>

    <div class="container">
        <h2>📤 数据发送测试</h2>
        <button class="button" onclick="sendTestDataWithRetry()">发送测试数据(带重试)</button>
        <button class="button" onclick="batchSendTest()">批量发送测试</button>
        <div id="sendStatus"></div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <button class="button" onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let currentTimeout = 60000; // 默认60秒
        let currentRetries = 3;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 检测网络环境
        async function detectNetworkEnvironment() {
            try {
                // 测试到不同服务器的延迟
                const tests = [
                    { name: 'Baidu', url: 'https://www.baidu.com', region: 'China' },
                    { name: 'Google', url: 'https://www.google.com', region: 'Global' },
                    { name: 'ERP服务器', url: 'https://erpapi.yxyglobal.com', region: 'Target' }
                ];

                let results = [];
                for (let test of tests) {
                    try {
                        const startTime = Date.now();
                        await fetch(test.url, { 
                            method: 'GET', 
                            mode: 'no-cors',
                            signal: AbortSignal.timeout(10000)
                        });
                        const latency = Date.now() - startTime;
                        results.push({ ...test, latency, success: true });
                    } catch (error) {
                        results.push({ ...test, latency: -1, success: false, error: error.message });
                    }
                }

                // 分析网络环境
                const baiduLatency = results.find(r => r.name === 'Baidu')?.latency || -1;
                const googleLatency = results.find(r => r.name === 'Google')?.latency || -1;
                const erpLatency = results.find(r => r.name === 'ERP服务器')?.latency || -1;

                let environment = '未知';
                let recommendedTimeout = 60;

                if (baiduLatency > 0 && baiduLatency < 100) {
                    environment = '国内网络';
                    recommendedTimeout = 30;
                } else if (googleLatency > 0 && googleLatency < 500) {
                    environment = '海外网络 (良好)';
                    recommendedTimeout = 60;
                } else if (googleLatency > 500 || erpLatency > 1000) {
                    environment = '海外网络 (高延迟)';
                    recommendedTimeout = 90;
                } else {
                    environment = '海外网络 (紫鸟浏览器)';
                    recommendedTimeout = 60;
                }

                document.getElementById('networkEnv').textContent = environment;
                document.getElementById('recommendedTimeout').textContent = `${recommendedTimeout} 秒`;
                document.getElementById('timezone').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;

                log(`🌐 网络环境检测完成: ${environment}`);
                log(`📊 延迟测试结果: Baidu=${baiduLatency}ms, Google=${googleLatency}ms, ERP=${erpLatency}ms`);

            } catch (error) {
                log(`❌ 网络环境检测失败: ${error.message}`);
            }
        }

        // 使用配置测试API
        async function testApiWithConfig() {
            currentTimeout = parseInt(document.getElementById('timeoutSetting').value) * 1000;
            currentRetries = parseInt(document.getElementById('retryCount').value);

            log(`🔧 使用配置: 超时=${currentTimeout/1000}秒, 重试=${currentRetries}次`);
            showStatus('apiStatus', '正在使用自定义配置测试API...', 'info');

            await testApiWithRetry();
        }

        // 带重试的API测试
        async function testApiWithRetry(attempt = 1) {
            try {
                log(`📡 API测试尝试 ${attempt}/${currentRetries}`);
                
                const startTime = Date.now();
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'overseas_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'overseas_network_test',
                            timeout_setting: currentTimeout,
                            retry_attempt: attempt
                        }]
                    }),
                    signal: AbortSignal.timeout(currentTimeout)
                });

                const duration = Date.now() - startTime;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ API测试成功 (尝试${attempt}, 耗时${duration}ms): ${JSON.stringify(result)}`);
                showStatus('apiStatus', `API连接成功！<br>尝试次数: ${attempt}<br>响应时间: ${duration}ms<br>结果: ${JSON.stringify(result)}`, 'success');
                return true;

            } catch (error) {
                log(`❌ 第${attempt}次API测试失败: ${error.message}`);
                
                if (attempt < currentRetries) {
                    log(`⏳ 5秒后进行第${attempt + 1}次重试...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    return await testApiWithRetry(attempt + 1);
                } else {
                    showStatus('apiStatus', `API测试失败！<br>重试${currentRetries}次后仍然失败<br>错误: ${error.message}`, 'error');
                    return false;
                }
            }
        }

        // 多超时测试
        async function testMultipleTimeouts() {
            log('🧪 开始多超时测试...');
            showStatus('apiStatus', '正在进行多超时测试...', 'info');

            const timeouts = [30, 60, 90, 120]; // 秒
            let results = [];

            for (let timeout of timeouts) {
                try {
                    log(`📡 测试 ${timeout} 秒超时...`);
                    const startTime = Date.now();
                    
                    const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            table_name: 'test_connection',
                            unique_key: ['test_id'],
                            validate_fields: ['test_id'],
                            database: 'rpa',
                            data: [{
                                test_id: 'timeout_test_' + Date.now(),
                                test_time: new Date().toISOString(),
                                timeout_setting: timeout
                            }]
                        }),
                        signal: AbortSignal.timeout(timeout * 1000)
                    });

                    const duration = Date.now() - startTime;
                    const result = await response.json();
                    
                    results.push({
                        timeout: timeout,
                        success: true,
                        duration: duration,
                        result: result
                    });
                    
                    log(`✅ ${timeout}秒超时测试成功 (${duration}ms)`);
                } catch (error) {
                    results.push({
                        timeout: timeout,
                        success: false,
                        error: error.message
                    });
                    
                    log(`❌ ${timeout}秒超时测试失败: ${error.message}`);
                }
            }

            // 显示结果
            let resultHtml = '<h4>多超时测试结果:</h4><ul>';
            results.forEach(r => {
                if (r.success) {
                    resultHtml += `<li style="color: green;">✅ ${r.timeout}秒: 成功 (${r.duration}ms)</li>`;
                } else {
                    resultHtml += `<li style="color: red;">❌ ${r.timeout}秒: 失败 - ${r.error}</li>`;
                }
            });
            resultHtml += '</ul>';

            const successfulTimeouts = results.filter(r => r.success);
            if (successfulTimeouts.length > 0) {
                const recommended = Math.min(...successfulTimeouts.map(r => r.timeout));
                resultHtml += `<p><strong>建议超时设置: ${recommended} 秒</strong></p>`;
            }

            showStatus('apiStatus', resultHtml, successfulTimeouts.length > 0 ? 'success' : 'error');
            log('🧪 多超时测试完成');
        }

        // 网络延迟测试
        async function testNetworkLatency() {
            log('🔍 开始网络延迟测试...');
            showStatus('apiStatus', '正在测试网络延迟...', 'info');

            const testCount = 5;
            let latencies = [];

            for (let i = 1; i <= testCount; i++) {
                try {
                    log(`📡 延迟测试 ${i}/${testCount}...`);
                    const startTime = Date.now();
                    
                    await fetch('https://erpapi.yxyglobal.com/', {
                        method: 'GET',
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(10000)
                    });
                    
                    const latency = Date.now() - startTime;
                    latencies.push(latency);
                    log(`📊 第${i}次延迟: ${latency}ms`);
                    
                    // 测试间隔1秒
                    if (i < testCount) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                } catch (error) {
                    log(`❌ 第${i}次延迟测试失败: ${error.message}`);
                }
            }

            if (latencies.length > 0) {
                const avgLatency = Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length);
                const minLatency = Math.min(...latencies);
                const maxLatency = Math.max(...latencies);
                
                let recommendation = '';
                if (avgLatency < 500) {
                    recommendation = '网络状况良好，建议使用30-60秒超时';
                } else if (avgLatency < 1000) {
                    recommendation = '网络延迟较高，建议使用60-90秒超时';
                } else {
                    recommendation = '网络延迟很高，建议使用90-120秒超时';
                }

                const resultHtml = `
                    <h4>网络延迟测试结果:</h4>
                    <ul>
                        <li>平均延迟: ${avgLatency}ms</li>
                        <li>最小延迟: ${minLatency}ms</li>
                        <li>最大延迟: ${maxLatency}ms</li>
                        <li>测试次数: ${latencies.length}/${testCount}</li>
                    </ul>
                    <p><strong>${recommendation}</strong></p>
                `;
                
                showStatus('apiStatus', resultHtml, 'success');
                log(`📊 延迟测试完成: 平均${avgLatency}ms`);
            } else {
                showStatus('apiStatus', '延迟测试失败，无法获取延迟数据', 'error');
                log('❌ 延迟测试完成，但没有成功的测试');
            }
        }

        // 发送测试数据(带重试)
        async function sendTestDataWithRetry() {
            currentTimeout = parseInt(document.getElementById('timeoutSetting').value) * 1000;
            currentRetries = parseInt(document.getElementById('retryCount').value);

            log(`📤 开始发送测试数据 (超时=${currentTimeout/1000}秒, 重试=${currentRetries}次)`);
            showStatus('sendStatus', '正在发送测试数据...', 'info');

            const testData = [{
                unique_id: 'overseas_test_' + Date.now(),
                platform_account: 'overseas_test_store',
                platform_site: 'Global',
                sku: 'OVERSEAS-TEST-001',
                asin: 'B0OVERSEAS01',
                type: '海外网络测试数据',
                status: 'Open',
                platform_time: Math.floor(Date.now() / 1000),
                platform_end_time: Math.floor(Date.now() / 1000) + 86400,
                create_time: Math.floor(Date.now() / 1000),
                brand: 'TestBrand',
                asin_num: 1,
                sku_num: 1,
                is_click: 0,
                has_product_safety_cert: 1,
                action_type: 'overseas_test',
                processed_time: new Date().toISOString(),
                network_config: {
                    timeout: currentTimeout,
                    retries: currentRetries,
                    test_time: new Date().toISOString()
                }
            }];

            await sendDataWithRetry(testData);
        }

        // 带重试的数据发送
        async function sendDataWithRetry(data, attempt = 1) {
            try {
                log(`📡 数据发送尝试 ${attempt}/${currentRetries}`);
                
                const startTime = Date.now();
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table_name: 'data_amazon_compliance',
                        unique_key: ['unique_id'],
                        validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
                        database: 'rpa',
                        data: data
                    }),
                    signal: AbortSignal.timeout(currentTimeout)
                });

                const duration = Date.now() - startTime;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ 数据发送成功 (尝试${attempt}, 耗时${duration}ms): ${JSON.stringify(result)}`);
                showStatus('sendStatus', `数据发送成功！<br>尝试次数: ${attempt}<br>响应时间: ${duration}ms<br>数据量: ${data.length}条`, 'success');
                return true;

            } catch (error) {
                log(`❌ 第${attempt}次数据发送失败: ${error.message}`);
                
                if (attempt < currentRetries) {
                    log(`⏳ 5秒后进行第${attempt + 1}次重试...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    return await sendDataWithRetry(data, attempt + 1);
                } else {
                    showStatus('sendStatus', `数据发送失败！<br>重试${currentRetries}次后仍然失败<br>错误: ${error.message}`, 'error');
                    return false;
                }
            }
        }

        // 批量发送测试
        async function batchSendTest() {
            log('📦 开始批量发送测试...');
            showStatus('sendStatus', '正在进行批量发送测试...', 'info');

            // 生成10条测试数据
            const batchData = [];
            for (let i = 1; i <= 10; i++) {
                batchData.push({
                    unique_id: `batch_test_${Date.now()}_${i}`,
                    platform_account: 'batch_test_store',
                    platform_site: 'Global',
                    sku: `BATCH-TEST-${String(i).padStart(3, '0')}`,
                    asin: `B0BATCH${String(i).padStart(3, '0')}`,
                    type: '批量测试数据',
                    status: 'Open',
                    platform_time: Math.floor(Date.now() / 1000),
                    platform_end_time: Math.floor(Date.now() / 1000) + 86400,
                    create_time: Math.floor(Date.now() / 1000),
                    brand: 'BatchTestBrand',
                    asin_num: 1,
                    sku_num: 1,
                    is_click: 0,
                    has_product_safety_cert: 1,
                    action_type: 'batch_test',
                    processed_time: new Date().toISOString()
                });
            }

            const success = await sendDataWithRetry(batchData);
            if (success) {
                log('✅ 批量发送测试完成');
            } else {
                log('❌ 批量发送测试失败');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 海外网络环境API测试工具已加载');
            detectNetworkEnvironment();
        });
    </script>
</body>
</html>
