# API连接故障排除指南

## 🚨 问题现象
```
❌ API连接测试失败: Failed to fetch
```

## 🔍 问题分析

`Failed to fetch` 错误通常由以下原因引起：

### 1. CORS（跨域资源共享）限制 ⭐ 最常见
- **现象**: 在浏览器测试页面中出现 `Failed to fetch`
- **原因**: 浏览器阻止了跨域请求
- **解决方案**: 在Chrome扩展环境中测试（扩展有特殊权限）

### 2. 网络连接问题
- **现象**: 所有网络请求都失败
- **原因**: 网络不通或防火墙阻止
- **解决方案**: 检查网络连接

### 3. 服务器不可达
- **现象**: 特定服务器无法访问
- **原因**: 服务器宕机或DNS问题
- **解决方案**: 检查服务器状态

## 🛠️ 解决步骤

### 步骤1: 网络诊断
1. 打开 `network_test.html` 进行网络诊断
2. 运行所有测试，查看结果
3. 如果基础网络测试失败，检查网络连接

### 步骤2: 使用Chrome扩展测试
1. 在Amazon页面中加载插件
2. 打开插件状态窗口
3. 点击"测试API"按钮
4. 查看控制台日志

### 步骤3: 使用代理测试（如果CORS限制）
1. 在 `test_api.html` 中勾选"使用CORS代理"
2. 点击"使用代理测试"
3. 如果提示需要启用代理，访问: https://cors-anywhere.herokuapp.com/corsdemo

### 步骤4: 手动验证API
使用以下curl命令测试API：
```bash
curl -X POST "https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1" \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "test_connection",
    "unique_key": ["test_id"],
    "validate_fields": ["test_id"],
    "database": "rpa",
    "data": [{
      "test_id": "manual_test_'$(date +%s)'",
      "test_time": "'$(date -Iseconds)'",
      "test_source": "manual_curl"
    }]
  }'
```

## 🔧 具体解决方案

### 方案1: 在Chrome扩展中测试（推荐）
```javascript
// 在Amazon页面的控制台中运行
chrome.runtime.sendMessage({ type: 'TEST_API' }, (response) => {
  console.log('API测试结果:', response);
});
```

### 方案2: 使用Postman或类似工具
1. 下载Postman
2. 创建POST请求到: `https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1`
3. 设置Headers: `Content-Type: application/json`
4. 设置Body为测试数据

### 方案3: 临时禁用CORS（仅用于测试）
1. 关闭所有Chrome窗口
2. 使用以下命令启动Chrome:
```bash
# Windows
chrome.exe --user-data-dir="C:/temp/chrome_dev" --disable-web-security --disable-features=VizDisplayCompositor

# Mac
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security

# Linux
google-chrome --user-data-dir="/tmp/chrome_dev_test" --disable-web-security
```
3. 在新窗口中测试API

## 📊 测试工具使用

### 1. network_test.html
- **用途**: 诊断网络连接问题
- **使用**: 直接在浏览器中打开
- **功能**: 测试基础网络和API连接

### 2. test_api.html
- **用途**: 完整的API功能测试
- **使用**: 在浏览器中打开，支持代理模式
- **功能**: 测试API连接、发送数据、自定义测试

### 3. Chrome扩展调试
- **用途**: 在真实环境中测试
- **使用**: 在Amazon页面中使用插件
- **功能**: 真实的API调用测试

## 🔍 日志分析

### 正常的API调用日志
```
🔍 测试API连接...
📡 步骤1: 测试基础连接...
📊 基础连接状态: 200
📡 步骤2: 测试API端点...
📊 API响应状态: 200 OK (1234ms)
✅ API连接测试成功: {"success": true}
```

### 异常的API调用日志
```
🔍 测试API连接...
📡 步骤1: 测试基础连接...
⚠️ 基础连接失败: Failed to fetch
📡 步骤2: 测试API端点...
❌ API连接测试失败: Failed to fetch
💡 可能的原因: 1. 网络连接问题 2. CORS限制 3. 服务器不可达
```

## 🚀 验证修复

### 1. 确认API连接正常
- Chrome扩展中的"测试API"按钮显示成功
- 控制台显示正确的响应数据
- 没有网络错误

### 2. 确认数据发送正常
- 插件运行时能正常发送数据
- 服务器接收到数据
- 数据库中有相应记录

### 3. 确认错误处理正常
- 网络错误时有适当的错误提示
- 失败的数据会保存到本地
- 可以手动重试发送

## 📞 获取帮助

如果以上步骤都无法解决问题，请提供以下信息：

1. **网络测试结果**: `network_test.html` 的完整测试结果
2. **浏览器信息**: Chrome版本、操作系统
3. **错误日志**: 完整的控制台错误信息
4. **网络环境**: 是否使用代理、防火墙设置
5. **测试结果**: 各种测试方法的具体结果

## 💡 预防措施

1. **定期测试**: 定期运行网络诊断工具
2. **监控日志**: 关注插件运行日志
3. **备份机制**: 确保本地数据备份正常
4. **网络稳定**: 确保网络连接稳定

---

**记住**: CORS限制是最常见的问题，在Chrome扩展环境中测试通常能解决大部分问题！
