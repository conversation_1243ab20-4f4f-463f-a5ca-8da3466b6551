// 扩展配置管理
import { saveToStorage, getFromStorage } from './storage.js';

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  // API 配置
  api: {
    enabled: false,
    baseUrl: 'https://your-api-server.com/api',
    timeout: 10000,
    retryAttempts: 3,
    apiKey: ''
  },
  
  // 店铺配置
  store: {
    name: 'Default Store',
    site: 'UK',
    domain: 'sellercentral.amazon.co.uk'
  },
  
  // 数据配置
  data: {
    retentionDays: 7,
    maxHistoryItems: 100,
    autoCleanup: true,
    backupEnabled: false
  },
  
  // 监控配置
  monitoring: {
    enabled: true,
    targetUrls: [
      '*://sellercentral.amazon.co.uk/*',
      '*://sellercentral.amazon.com/*'
    ],
    apiEndpoints: [
      '/performance/api/product/policy/defects/pagination'
    ]
  },
  
  // 日志配置
  logging: {
    level: 'info', // debug, info, warn, error
    consoleEnabled: true,
    storageEnabled: true,
    maxLogEntries: 1000
  },
  
  // UI 配置
  ui: {
    theme: 'light', // light, dark
    language: 'zh_CN', // zh_CN, en
    notifications: true,
    popupSize: { width: 400, height: 600 }
  }
};

/**
 * 配置管理类
 */
class ConfigManager {
  constructor() {
    this.config = null;
    this.listeners = [];
  }
  
  /**
   * 初始化配置
   */
  async initialize() {
    try {
      // 尝试从存储中获取配置
      this.config = await getFromStorage('extensionConfig');
      
      // 如果没有配置，使用默认配置
      if (!this.config) {
        this.config = { ...DEFAULT_CONFIG };
        await this.save();
        console.log('⚙️ Default configuration initialized');
      } else {
        // 合并默认配置，确保新字段存在
        this.config = this.mergeConfig(DEFAULT_CONFIG, this.config);
        await this.save();
        console.log('⚙️ Configuration loaded and merged');
      }
      
      return this.config;
    } catch (error) {
      console.error('❌ Failed to initialize configuration:', error);
      this.config = { ...DEFAULT_CONFIG };
      return this.config;
    }
  }
  
  /**
   * 获取配置
   * @param {string} key - 配置键路径 (例如: 'api.baseUrl')
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  get(key, defaultValue = null) {
    if (!this.config) {
      return defaultValue;
    }
    
    const keys = key.split('.');
    let value = this.config;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }
  
  /**
   * 设置配置
   * @param {string} key - 配置键路径
   * @param {any} value - 配置值
   */
  async set(key, value) {
    if (!this.config) {
      await this.initialize();
    }
    
    const keys = key.split('.');
    const lastKey = keys.pop();
    let target = this.config;
    
    // 创建路径
    for (const k of keys) {
      if (!target[k] || typeof target[k] !== 'object') {
        target[k] = {};
      }
      target = target[k];
    }
    
    // 设置值
    target[lastKey] = value;
    
    // 保存配置
    await this.save();
    
    // 通知监听器
    this.notifyListeners(key, value);
  }
  
  /**
   * 重置配置为默认值
   */
  async reset() {
    this.config = { ...DEFAULT_CONFIG };
    await this.save();
    console.log('🔄 Configuration reset to defaults');
  }
  
  /**
   * 保存配置到存储
   */
  async save() {
    try {
      this.config.lastUpdated = new Date().toISOString();
      await saveToStorage('extensionConfig', this.config);
      console.log('💾 Configuration saved');
    } catch (error) {
      console.error('❌ Failed to save configuration:', error);
      throw error;
    }
  }
  
  /**
   * 合并配置
   * @param {Object} defaultConfig - 默认配置
   * @param {Object} userConfig - 用户配置
   * @returns {Object} 合并后的配置
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    for (const [key, value] of Object.entries(userConfig)) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        merged[key] = this.mergeConfig(defaultConfig[key] || {}, value);
      } else {
        merged[key] = value;
      }
    }
    
    return merged;
  }
  
  /**
   * 添加配置变化监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.push(listener);
  }
  
  /**
   * 移除配置变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  /**
   * 通知所有监听器
   * @param {string} key - 变化的配置键
   * @param {any} value - 新的配置值
   */
  notifyListeners(key, value) {
    this.listeners.forEach(listener => {
      try {
        listener(key, value, this.config);
      } catch (error) {
        console.error('❌ Configuration listener error:', error);
      }
    });
  }
  
  /**
   * 获取完整配置
   * @returns {Object} 完整配置对象
   */
  getAll() {
    return this.config ? { ...this.config } : null;
  }
  
  /**
   * 验证配置
   * @returns {Object} 验证结果
   */
  validate() {
    const errors = [];
    const warnings = [];
    
    if (!this.config) {
      errors.push('Configuration not initialized');
      return { errors, warnings, valid: false };
    }
    
    // 验证 API 配置
    if (this.config.api?.enabled) {
      if (!this.config.api.baseUrl || this.config.api.baseUrl === DEFAULT_CONFIG.api.baseUrl) {
        warnings.push('API base URL not configured');
      }
      if (!this.config.api.apiKey) {
        warnings.push('API key not configured');
      }
    }
    
    // 验证店铺配置
    if (!this.config.store?.name || this.config.store.name === DEFAULT_CONFIG.store.name) {
      warnings.push('Store name not configured');
    }
    
    // 验证监控配置
    if (!this.config.monitoring?.targetUrls?.length) {
      warnings.push('No target URLs configured for monitoring');
    }
    
    return {
      errors,
      warnings,
      valid: errors.length === 0
    };
  }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

// 导出配置管理器和便捷方法
export default configManager;

export const getConfig = (key, defaultValue) => configManager.get(key, defaultValue);
export const setConfig = (key, value) => configManager.set(key, value);
export const getAllConfig = () => configManager.getAll();
export const validateConfig = () => configManager.validate();
export const resetConfig = () => configManager.reset(); 