# -*- coding: utf-8 -*-
# 从任务表格读取店铺和站点，执行任务
import hashlib
from openpyxl.styles import Font
import openpyxl
import configparser
import pandas as pd
from urllib.parse import urlparse
import pymysql
from loguru import logger
from DrissionPage import Chromium, ChromiumOptions
import re
import psutil
import random
import subprocess
import requests
import json
import uuid
import traceback
from datetime import datetime, timedelta
import time
import shutil
import platform
import os
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')


# # 添加文件处理器，将日志写入文件
logger.add(
    sink="logfile.log",  # 日志文件的名称
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",  # 定义日志格式
    rotation="500 MB",  # 当文件大小达到 500 MB 时，自动创建新文件
    compression="zip"  # 旧文件压缩为 zip 格式
)

class MySQLClient:
    def __init__(self, host, user, password, database, port=3306):
        self.conn = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=port,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        self.cursor = self.conn.cursor()

    def query(self, sql, params=None):
        self.cursor.execute(sql, params or ())
        return self.cursor.fetchall()

    def batch_upsert(self, sql, data_list):
        """
        批量插入或更新数据。
        sql 示例:
        INSERT INTO table_name (col1, col2, col3) VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE col2=VALUES(col2), col3=VALUES(col3)
        """
        self.cursor.executemany(sql, data_list)
        self.conn.commit()

    def close(self):
        self.cursor.close()
        self.conn.close()




def write_dict_list_to_xlsx_pandas(browser_list, filename='output.xlsx'):
    if not browser_list:
        return
    # 将列表转换为 DataFrame
    df = pd.DataFrame(browser_list)
    # 将 DataFrame 写入 Excel 文件
    df.to_excel(filename, index=False)


# logger.remove()  # 移除默认的控制台处理器
# logger.add(
#     sink=lambda msg: print(msg, end=''),  # 定义日志输出到控制台
#     format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"  # 定义日志格式
# )
class TaskConfig():
    def __init__(self) -> None:
        self.config = self.get_config()

    def get_config(self):
        """
        读取当前目录下的config.ini配置文件，返回字典
        """
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(
            os.path.abspath(__file__)), 'config.ini')
        if not os.path.exists(config_path):
            logger.error(f"配置文件未找到: {config_path}")
            return {}
        config.read(config_path, encoding='utf-8')
        config_dict = {section: dict(config.items(section))
                       for section in config.sections()}
        return config_dict


class ExcelHandler:
    def __init__(self, file_path):
        self.file_path = file_path

    def read_sheet(self, sheet_name=0):
        """
        读取整个表格或指定sheet，并将每一行转为字典列表（第一行为表头）
        :param sheet_name: sheet名称或索引，默认第一个sheet
        :return: List[Dict]
        """
        try:
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            # DataFrame转为字典列表
            return df.fillna("").to_dict(orient="records")
        except Exception as e:
            print(f"读取表格时出错: {e}")
            return []

    def write_cell(self, row, col, value, sheet_name="截图店铺信息", type=None):
        """
        只在指定sheet（默认“截图店铺信息”）的指定行、列写入内容，不新建sheet。
        :param row: 行号（从0开始，实际写入时+2，因为有表头）
        :param col: 列号（从0开始）或列名（如"执行结果"）
        :param value: 要写入的值
        :param sheet_name: sheet名称，默认“截图店铺信息”
        :param type: 类型，'error'时字体为红色，否则为黑色
        """
        try:
            wb = openpyxl.load_workbook(self.file_path)
            ws = wb[sheet_name]  # 直接用sheet名
            # 如果col是字符串，找到对应的列号
            if isinstance(col, str):
                # 找到表头行（第一行）对应的列号
                for idx, cell in enumerate(ws[1], 1):
                    if cell.value == col:
                        col_idx = idx
                        break
                else:
                    raise Exception(f"未找到列名: {col}")
            else:
                col_idx = col + 1  # openpyxl列号从1开始
            cell = ws.cell(row=row + 2, column=col_idx)  # +2: 1表头，1从0开始
            cell.value = value
            if type == 'error':
                cell.font = Font(color="FF0000")
            else:
                cell.font = Font(color="000000")
            wb.save(self.file_path)
            print("写入成功")
        except Exception as e:
            print(f"写入单元格时出错: {e}")


# 公共类
class Common():
    def __init__(self):
        pass

    def find_available_port(self):
        while True:
            port = random.randint(10000, 20000)
            # 检查端口是否被占用
            if not self.is_port_in_use(port):
                return port

    def is_port_in_use(self, port):
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return True
        return False

    def find_dict_by_browser_name(self, dicts_list, target_name):
        """
        从字典列表中查找 browserName 符合指定字符串的字典
        :param dicts_list: 包含多个字典的列表
        :param target_name: 要查找的 browserName 的目标字符串
        :return: 找到的字典，如果未找到返回 None
        """
        for dictionary in dicts_list:
            if dictionary.get("browserName", "") == target_name or dictionary.get("store_username") == target_name:
                return dictionary
        return False

    def get_domain_of_existing_page(self, url):
        """
        获取网址的域名
        :param url: 传入的网址
        :return: 返回的域名
        """
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        return domain

# 紫鸟封装类


class superBrowser():
    def __init__(self, socket_port):
        self.matching_requests = []
        self.socket_port = socket_port

    def kill_process(self):
        """
        杀紫鸟客户端进程
        """
        logger.info("正在杀死所有紫鸟客户端进程......")
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 忽略大小写，判断进程名中是否包含 'superbrowser'
                if 'superbrowser' in proc.info['name'] or 'ziniao' in proc.info['name']:
                    logger.info(
                        f"终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    proc.terminate()  # 终止进程
                    proc.wait()  # 等待进程终止
                    logger.info(f"进程 {proc.info['pid']} 已终止")
                # os.system('taskkill /f /t /im SuperBrowser.exe')
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 捕获进程已结束或没有权限的情况
                pass

    def kill_main_process(self):
        """
        杀紫鸟客户端进程
        """
        logger.info("正在杀死所有紫鸟客户端进程......")
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 忽略大小写，判断进程名中是否包含 'superbrowser'
                if 'SuperBrowser' in proc.info['name']:
                    logger.info(
                        f"终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    proc.terminate()  # 终止进程
                    proc.wait()  # 等待进程终止
                    logger.info(f"进程 {proc.info['pid']} 已终止")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 捕获进程已结束或没有权限的情况
                pass

    def start_browser(self, client_path, socket_port):
        """
        启动客户端
        :return:
        """
        try:

            cmd = [client_path, '--run_type=web_driver',
                   '--ipc_type=http', '--port=' + str(socket_port)]

            subprocess.Popen(cmd)
            time.sleep(5)
        except Exception:
            print('start browser process failed')
            return

    def check_client_running(self, user_info, socket_port):
        """
        检查客户端是否正在运行
        :param user_info: 用户信息字典
        :param socket_port: 客户端socket端口
        :return: True/False
        """
        data = {
            "action": "updataCore",
            "requestId": str(uuid.uuid4()),
        }
        data.update(user_info)
        try:
            result = self.send_http(data, socket_port)
            if result is None:
                return False
            if result.get("statusCode") == 0:
                return True

        except Exception as e:
            logger.error(f"检查客户端运行状态异常: {e}")
            return False

    def update_core(self, user_info, socket_port):
        """
        下载所有内核，打开店铺前调用，需客户端版本5.285.7以上
        因为http有超时时间，所以这个action适合循环调用，直到返回成功
        """
        data = {
            "action": "updataCore",
            "requestId": str(uuid.uuid4()),
        }
        data.update(user_info)
        while True:
            result = self.send_http(data, socket_port)
            print(result)
            if result is None:
                print("等待客户端启动...")
                time.sleep(2)
                continue
            if result.get("statusCode") is None or result.get("statusCode") == -10003:
                print("当前版本不支持此接口，请升级客户端")
                return
            elif result.get("statusCode") == 0:
                print("更新内核完成")
                return
            else:
                print(f"等待更新内核: {json.dumps(result)}")
                time.sleep(2)

    def update_core(self, user_info, socket_port):
        """
        下载所有内核，打开店铺前调用，需客户端版本5.285.7以上
        因为http有超时时间，所以这个action适合循环调用，直到返回成功
        """
        data = {
            "action": "updataCore",
            "requestId": str(uuid.uuid4()),
        }
        data.update(user_info)
        time.sleep(2)
        # while True:
        result = self.send_http(data, socket_port)

        logger.info(result)
        if result is None:
            logger.info("等待客户端启动...")
            time.sleep(2)
            raise Exception("客户端未启动")
        if result.get("statusCode") is None or result.get("statusCode") == -10003:
            logger.error("当前版本不支持此接口，请升级客户端")
            return "VERSION ERROR"
        elif result.get("statusCode") == 0:
            logger.info("更新内核完成")
            return True
        else:
            logger.info(f"等待更新内核: {json.dumps(result)}")
            time.sleep(2)

    def send_http(self, data, socket_port):
        """
        通讯方式
        :param data:
        :return:
        """
        try:
            # logger.info(f"发送数据: {json.dumps(data)}, socket_port: {socket_port}")
            url = 'http://127.0.0.1:{}'.format(socket_port)
            print(json.dumps(
                data).encode('utf-8'))
            response = requests.post(url, json.dumps(
                data).encode('utf-8'), timeout=120)
            logger.info(f"返回数据: {response.text}")
            return json.loads(response.text)
        except Exception as err:
            logger.error(err)

    def delete_all_cache(self):
        """
        删除所有店铺缓存
        非必要的，如果店铺特别多、硬盘空间不够了才要删除
        """
        if not platform.system() == 'Windows':
            return
        local_appdata = os.getenv('LOCALAPPDATA')
        cache_path = os.path.join(local_appdata, 'SuperBrowser')
        if os.path.exists(cache_path):
            shutil.rmtree(cache_path)

    def delete_all_cache_with_path(self, path):
        """
        :param path: 启动客户端参数使用--enforce-cache-path时设置的缓存路径
        删除所有店铺缓存
        非必要的，如果店铺特别多、硬盘空间不够了才要删除
        """
        if not platform.system() == 'Windows':
            return
        cache_path = os.path.join(path, 'SuperBrowser')
        if os.path.exists(cache_path):
            shutil.rmtree(cache_path)

    def open_store(self, store_info, isWebDriverReadOnlyMode=0, isprivacy=0, isHeadless=0, cookieTypeSave=0, jsInfo="", user_info={}):
        while True:
            request_id = str(uuid.uuid4())
            data = {
                "action": "startBrowser",
                # "isWaitPluginUpdate": 0,
                "isHeadless": isHeadless,
                "requestId": request_id,
                "isWebDriverReadOnlyMode": isWebDriverReadOnlyMode,
                "cookieTypeLoad": 0,
                "cookieTypeSave": cookieTypeSave,
                "runMode": "3",
                # "isLoadUserPlugin": False,
                "pluginIdType": 1,
                "privacyMode": isprivacy
            }
            data.update(user_info)

            if store_info.isdigit():
                data["browserId"] = store_info
            else:
                data["browserOauth"] = store_info

            if len(str(jsInfo)) > 2:
                data["injectJsInfo"] = json.dumps(jsInfo)
            print("data", data)
            r = self.send_http(data, self.socket_port)
            if str(r.get("statusCode")) == "0":
                return r
            elif str(r.get("statusCode")) == "-10003":
                logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
                continue
                # return False
            elif str(r.get("statusCode")) == "1":
                logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")

                break
            else:
                logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
                continue
        return False

    def close_store(self, browser_oauth, user_info={}):
        request_id = str(uuid.uuid4())
        data = {
            "action": "stopBrowser",
            "requestId": request_id,
            "duplicate": 0,
            "browserOauth": browser_oauth
        }
        data.update(user_info)

        r = self.send_http(data, self.socket_port)
        if str(r.get("statusCode")) == "0":
            return r
        elif str(r.get("statusCode")) == "-10003":
            logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
            exit()
        else:
            logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
            exit()

    def get_browser_list(self, user_info={}):
        while True:
            request_id = str(uuid.uuid4())
            data = {
                "action": "getBrowserList",
                "requestId": request_id
            }
            data.update(user_info)

            r = self.send_http(data, self.socket_port)
            logger.info(r)
            if str(r.get("statusCode")) == "0":
                # logger.info(r)
                return r.get("browserList")
            elif str(r.get("statusCode")) == "-10003":
                logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
                exit()
            else:
                logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
                time.sleep(2)

    def get_browser_context(self, port):
        """
        获取 drissionpage 浏览器会话
        """
        # 创建 ChromiumOptions 对象并设置参数
        co = ChromiumOptions()
        co.set_argument('--ignore-certificate-errors')
        co.set_argument('--ignore-ssl-errors')
        co.set_argument('--no-sandbox')
        co.set_argument('--disable-gpu')  # 禁止GPU加速
        co.set_local_port(port)

        logger.info(f"店铺端口-{port}")
        page = Chromium(addr_or_opts=co).latest_tab
        return page

    def open_ip_check(self, browser_context, ip_check_url):
        """
        打开ip检测页检测ip是否正常
        :param browser_context: drissionpage 浏览器会话
        :param ip_check_url ip检测页地址
        :return 检测结果
        """
        try:
            browser_context.get(ip_check_url)
            success_button = browser_context.ele(
                '//button[contains(@class, "styles_btn--success")]')
            success_button.wait_for_appear(timeout=60)  # 等待查找元素60秒
            logger.info("ip检测成功")
            return True
        except Exception as e:
            logger.error("ip检测异常:" + traceback.format_exc())
            return False

    def open_launcher_page(self, browser_context, launcher_page):
        browser_context.get(
            launcher_page+"?mons_sel_locale=zh_CN&languageSwitched=1")
        time.sleep(6)

    def get_exit(self, user_info={}):
        """
        关闭客户端
        :return:
        """
        data = {"action": "exit", "requestId": str(uuid.uuid4())}
        data.update(user_info)
        # logger.info('@@ get_exit...' + json.dumps(data))
        self.send_http(data, self.socket_port)

    def use_one_browser_run_task(self, browser, user_info={}):
        """
        打开一个店铺运行脚本
        :param browser: 店铺信息
        """
        # 如果要指定店铺ID, 获取方法:登录紫鸟客户端->账号管理->选择对应的店铺账号->点击"查看账号"进入账号详情页->账号名称后面的ID即为店铺ID
        store_id = browser.get('browserOauth')
        store_name = browser.get("browserName")
        # 打开店铺
        logger.info(f"=====打开店铺：{store_name}=====")
        ret_json = self.open_store(store_id, user_info=user_info)
        if not ret_json:
            return False
        # logger.info(ret_json)
        store_id = ret_json.get("browserOauth")
        if store_id is None:
            store_id = ret_json.get("browserId")
        # print(ret_json)
        # 获取 drissionpage 浏览器会话
        browser_context = self.get_browser_context(
            ret_json.get('debuggingPort'))
        if browser_context is None:
            logger.info(f"=====关闭店铺：{store_name}=====")
            self.close_store(store_id, user_info=user_info)
            return False
        logger.info(f"打开店铺主页=={ret_json.get('launcherPage')}")

        if "amazon" not in ret_json.get("launcherPage"):
            logger.info(f"店铺主页地址异常: {ret_json.get('launcherPage')}")
            logger.error("店铺主页地址异常，请检查")
            return False
        time.sleep(2)
        self.open_launcher_page(browser_context, ret_json.get("launcherPage"))
        time.sleep(3)
        is_switch_account = browser_context.ele(
            "#ap-account-switcher-container", timeout=2)
        if is_switch_account:
            logger.info("账号切换页")
            switch_account_link = browser_context.ele(
                '//a[@data-name="switch_account_request"]', timeout=2)
            if switch_account_link:
                switch_account_link.click()
                logger.info("成功点击切换账号链接")
            else:
                logger.error("未找到切换账号链接")
        signInSubmit = browser_context.ele("#signInSubmit", timeout=2)
        if signInSubmit:
            signInSubmit.click()
        signInSubmit = browser_context.ele("#signInSubmit", timeout=2)
        if signInSubmit:
            signInSubmit.click()
            time.sleep(1)
        otp_code = browser_context.ele("#auth-mfa-otpcode", timeout=2)
        if otp_code:
            time.sleep(1)
            browser_context.ele("#auth-signin-button").click()
        otp_code = browser_context.ele("#auth-signin-button", timeout=2)
        if otp_code:
            time.sleep(1)
            browser_context.ele("#auth-signin-button").click()
        return browser_context

        # # 获取ip检测页地址
        # ip_check_url = ret_json.get("ipDetectionPage")
        # if not ip_check_url:
        #     logger.error("ip检测页地址为空，请升级紫鸟浏览器到最新版")
        #     logger.info(f"=====关闭店铺：{store_name}=====")
        #     self.close_store(store_id, user_info=user_info)
        #     exit()
        # # 执行脚本
        # try:
        #     ip_usable = self.open_ip_check(browser_context, ip_check_url)
        #     if ip_usable:
        #         logger.info("ip检测通过，打开店铺平台主页")
        #         self.open_launcher_page(browser_context, ret_json.get("launcherPage"))
        #         # 打开店铺平台主页后进行后续自动化操作
        #     else:
        #         logger.info("ip检测不通过，请检查")
        # except:
        #     logger.error("脚本运行异常:" + traceback.format_exc())
        # finally:
        #     logger.info(f"=====关闭店铺：{store_name}=====")
        #     self.close_store(store_id, user_info=user_info)

    def use_all_browser_run_task(self, browser_list, user_info={}):
        """
        循环打开所有店铺运行脚本
        :param browser_list: 店铺列表
        """
        for browser in browser_list:
            self.use_one_browser_run_task(browser, user_info)

    def get_browser_site(self, page, domain):
        try:
            self.page = page
            self.page.listen.start(
                targets=True, method="GET")

            self.page.get(
                f"https://{domain}/account-switcher/default/merchantMarketplace")

            for packet in self.page.listen.steps():
                if "account-switcher/regional-accounts/merchantMarketplace?globalAccountId=" in packet.url and "regionalAccounts" in packet.response.body:
                    logger.info("站点获取成功")
                    self.page.listen.stop()
                    return packet.response.body

            logger.error("站点获取异常")
            raise Exception("站点获取异常")
            # break
        except Exception as e:
            raise Exception(e)


# 主任务类


class Task():
    def __init__(self, page, store, site):
        self.page = page
        self.store = store
        self.site = site


    def convert_utc_to_cst(self,utc_str):
        # 解析UTC时间字符串
        utc_time = datetime.strptime(utc_str, "%Y-%m-%dT%H:%M:%S.%fZ")

        # 转换为中国标准时间 (UTC+8)
        cst_time = utc_time + timedelta(hours=8)

        # 格式化为年月日
        return cst_time.strftime("%Y-%m-%d")

    def md5_encrypt(self,text):
        # 创建 MD5 对象
        md5 = hashlib.md5()

        # 更新加密内容，确保是字节类型
        md5.update(text.encode('utf-8'))

        # 获取加密结果，返回十六进制字符串
        return md5.hexdigest()

    def get_num(self,issueList):
        # issueList = args.get('issueList')
        asin_set = set()
        artifact_id_set = set()

        for item in issueList:
            asin = item.get("parameters", {}).get("asin")
            artifact_id = item.get("target", {}).get("artifactId")

            if asin:
                asin_set.add(asin)
            if artifact_id:
                artifact_id_set.add(artifact_id)
        return len(asin_set),len(artifact_id_set)
    def click_next_page(self):
        """
        点击页面的下一页按钮，返回是否点击成功
        """
        click_result = self.page.run_js('''
            const katPagination = document.querySelector('kat-pagination#ahd-pp-pagination-katal-control');
            if (katPagination && katPagination.shadowRoot) {
                const shadow = katPagination.shadowRoot;
                const nextPageBtn = shadow.querySelector('span[part="pagination-nav-right"].nav.item');
                if (nextPageBtn) {
                    nextPageBtn.click();
                    return true;
                }
            }
            return false;
        ''')
        return click_result

    def wait_for_page_load(self, timeout=10):
        """
        等待页面加载完成，返回是否加载成功
        """
        try:
            # 等待一段时间让页面加载
            time.sleep(2)

            # 检查页面是否有加载指示器
            loading_indicators = self.page.eles("tag:div@@class*=loading", timeout=3)
            if loading_indicators:
                logger.info("检测到页面正在加载，等待完成...")
                for _ in range(timeout * 2):  # 每0.5秒检查一次
                    loading_indicators = self.page.eles("tag:div@@class*=loading", timeout=1)
                    if not loading_indicators:
                        logger.info("页面加载完成")
                        return True
                    time.sleep(0.5)
                logger.warning("页面加载超时")
                return False

            # 检查页面是否有错误提示
            error_elements = self.page.eles("tag:div@@class*=error", timeout=3)
            if error_elements:
                for error_ele in error_elements:
                    error_text = error_ele.text
                    if "网络" in error_text or "network" in error_text.lower() or "error" in error_text.lower():
                        logger.error(f"检测到页面错误: {error_text}")
                        return False

            logger.info("页面加载状态正常")
            return True
        except Exception as e:
            logger.error(f"检查页面加载状态时出错: {e}")
            return False

    def run(self):
        common_task = Common()
        domain = common_task.get_domain_of_existing_page(url=self.page.url)
        logger.info(f"当前页面的域名是: {domain}")
        try:
            invaid_text_ele = self.page.ele("tag:div@@class=invalid-charge-method-container", timeout=3)
            if invaid_text_ele:
                h1_ele = invaid_text_ele.ele("tag:h1@@text()=付款方式无效", timeout=2)
                if h1_ele:
                    invaid_text = h1_ele.text
                    raise Exception(invaid_text)
        except Exception as e:
            logger.info(f"未检测到无效付款方式提示或查找元素异常: {e}")


        self.page.listen.set_targets(targets=True)
        self.page.listen.start(targets=True)
        self.page.get(
            f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance")

        # 数据总量
        total_num = ""
        current_index = 25
        normal_data_map = {}
        db = MySQLClient(
            host='*************',
            user='root',
            password='123456',
            database='rpa'
        )
        store_name = self.store
        store_site = self.site
        no_clicked_sku_list = []
        clicked_sku_list = []
        current_asins = []
        total_progress = 0  # 全局计数器，统计所有分页累计处理的defect条数
        consecutive_failed_pages = 0  # 连续翻页失败计数器
        max_consecutive_failed_pages = 3  # 最大连续翻页失败次数
        for packet in self.page.listen.steps():
            if packet.url and "performance/api/summary" in packet.url:
                total_num = packet.response.body.get("listingLevelMetrics", {}).get(
                    "REGULATORY_COMPLIANCE", {}).get("defects", {}).get("count", "")
                logger.info(f"数据总量: {total_num}")
                if total_num == 0:
                    logger.info("没有数据")
                    self.page.listen.stop()
                    return
                # elif total_num > 20000:
                #     logger.error("数据量过大，请手动下载")
                #     self.page.listen.stop()
                #     raise Exception("数据量大于5000，请手动下载")

            # print(packet.url)
            if packet.url and "zh-CN.json" in packet.url:
                for key, value in packet.response.body.items():
                    normal_data_map[key] = value
                # logger.info(f"normal_data_map: {normal_data_map}")
            if packet.url and "performance/api/product/policy/defects/pagination?metricNames=" in packet.url and "statuses=Open" in packet.url:
                sellerId = packet.response.body.get("sellerId", "")
                if not sellerId:
                    logger.error("未获取到sellerId")
                    self.page.listen.stop()
                    break
                defects = packet.response.body.get("defects", [])
                # logger.info(f"defects: {defects}")

                if not defects:
                    logger.info("没有产品政策数据")
                    # 继续尝试下一页，不停止处理
                    continue
                else:
                    logger.info("产品政策数据获取成功")
                    logger.info(f"当前数据量: {len(defects)}")
                    loop_index = 0
                    valid_data_count = 0  # 当前页面有效数据计数
                    skipped_count = 0  # 跳过数据计数
                    for loop_item in defects:
                        try:
                            loop_index = loop_index + 1
                            total_progress += 1
                            insert_data = {}
                            msg_type = loop_item.get("issueList",[])[0].get("parameters",{}).get("message_id","")
                            msg = normal_data_map.get(msg_type, "")
                            if msg_type != "sp_health_detail_table_reason_psi_details":
                                logger.info(f"跳过非政策详情数据: {msg_type} (进度: {total_progress}/{total_num}, 当前页面: {loop_index}/{len(defects)})")
                                skipped_count += 1
                                continue
                            valid_data_count += 1  # 找到有效数据，计数器加1
                            policyEntityId = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("policy_id", "")
                            sku = loop_item.get("issueList")[0].get("target",{}).get("artifactId","")
                            logger.info(f"处理数据: policyEntityId: {policyEntityId}, sku: {sku}, msg: {msg}")
                            if sku in clicked_sku_list:
                                logger.info(f"已处理过的SKU: {sku}, 跳过")
                                continue
                            asin = loop_item.get("issueList")[0].get("parameters",{}).get("asin","")
                            current_asins.append(asin)
                            start_time =  loop_item.get("issueList", [{}])[0].get("parameters", {}).get("impactDate")
                            due_time = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("due_date")
                            brand = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("brand", "")
                            skus,asins = self.get_num(loop_item.get("issueList"))
                            if start_time == "" or start_time is None:
                                start_time = 0
                            else:
                                # 将时间字符串 "2025年4月4日" 转换为秒级时间戳
                                try:
                                    # 先将中文日期转为标准日期字符串
                                    if "年" in start_time and "月" in start_time and "日" in start_time:
                                        year = int(start_time.split("年")[0])
                                        month = int(start_time.split("年")[1].split("月")[0])
                                        day = int(start_time.split("月")[1].split("日")[0])
                                        dt = time.strptime(f"{year}-{month:02d}-{day:02d}", "%Y-%m-%d")
                                        # 转为时间戳（假设为中国时区0点）
                                        start_time = int(time.mktime(dt))
                                    else:
                                        dt = time.strptime(start_time, "%Y-%m-%d")
                                        start_time = int(time.mktime(dt))
                                except Exception as e:
                                    logger.error(f"时间格式化出错: {e}")
                                    start_time = start_time
                            if due_time == "" or due_time is None:
                                due_time = 0
                            else:
                                due_time = self.convert_utc_to_cst(due_time)
                                # 将due_time字符串（格式为"%Y-%m-%d"）转换为中国时区的秒级时间戳
                                try:
                                    dt = time.strptime(due_time, "%Y-%m-%d")
                                    due_time = int(time.mktime(dt))
                                except Exception as e:
                                    logger.error(f"due_time格式化出错: {e}")
                                    due_time = due_time
                            # 显示任务进度
                            progress = loop_index

                            logger.info(f"任务进度: {total_progress}/{total_num} (当前页面: {loop_index}/{len(defects)})")
                            logger.info(f"store_name: {store_name}")
                            logger.info(f"store_site: {store_site}")
                            logger.info(f"msg: {msg}")
                            logger.info(f"asin: {loop_item.get('asin')}")
                            logger.info(f"sku: {sku}")
                            logger.info(f"status: {loop_item.get('status')}")
                            logger.info(f"start_time: {start_time}")
                            logger.info(f"due_time: {due_time}")
                            logger.info(store_name + store_site + msg + loop_item.get('asin') + sku + loop_item.get('status') + str(start_time) + str(due_time))
                            md5_value = self.md5_encrypt(store_name + store_site +msg + loop_item.get('asin') + sku + loop_item.get('status') + str(start_time)  + str(due_time))
                            logger.info(f"MD5值: {md5_value}")
                            query_sql = """SELECT * FROM data_amazon_compliance WHERE unique_id = %s AND is_click IN (-1, 1)
            """
                            query_exist = db.query(query_sql, (md5_value,))
                            if query_exist:
                                logger.info(f"{store_name}-{store_site}-已点击过的SKU: {sku}, 跳过")

                                continue
                            else:
                                logger.info(f"{store_name}-{store_site}-未点击过的SKU: {sku}, 继续处理")
                                    # # 批量 upsert 示例
                                current_timestamp = int(time.time())
                                logger.info(f"当前时间的秒级时间戳: {current_timestamp}")
                                data_sku = sku
                                timestamp = current_timestamp

                                # 获取 policy_name 用于判断风险等级
                                policy_name = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("policy_name", "")

                                # 根据 policy_name 判断风险等级并设置 has_product_safety_cert
                                if policy_name == "GPSR_Reactive_HighRisk_PSI_DE":
                                    has_product_safety_cert = 2
                                elif policy_name == "GPSR_Reactive_MedLow-Risk_PSI_DE":
                                    has_product_safety_cert = 1
                                else:
                                    has_product_safety_cert = 0

                                insert_data = {
                                    "unique_id": md5_value,
                                    "platform_account": store_name,
                                    "platform_site": store_site,
                                    "type": msg,
                                    "asin": loop_item.get('asin', ""),
                                    "sku": data_sku,
                                    "status": loop_item.get('status'),
                                    "platform_time": start_time,
                                    "platform_end_time": due_time,
                                    "create_time": timestamp,
                                    "brand": brand,
                                    "asin_num": asins,
                                    "sku_num": skus,
                                    "is_click": 0,
                                    "has_product_safety_cert": has_product_safety_cert
                                }
                                if not sku:
                                    logger.error("sku为空，跳过")
                                    continue
                                if not policyEntityId:
                                    logger.error("policyEntityId为空，跳过")
                                    continue
                                logger.info(f"处理policyEntityId: {policyEntityId}")
                                # 构建js请求，取得绩效政策详情
                                headers = {
                                    "authority": domain,
                                    "accept": "*/*",
                                    "accept-language": "zh-TW,zh;q=0.9",
                                    "anti-csrftoken-a2z-request": "true",
                                    "Content-Type": "application/json",
                                    "origin": f"https://{domain}",
                                    "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                }
                                js_code = f"""
                                window._defectDetail = undefined;
                                fetch('https://{domain}/spx/myc/myc-backend-service/api/search-scpm-policies', {{
                                    method: 'POST',
                                    headers: {json.dumps(headers)},
                                    body: JSON.stringify({{"policyEntityId": {policyEntityId}}})
                                }})
                                .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                """
                                self.page.run_js(js_code)
                                for _ in range(120):
                                    detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                    antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                    if detail is not None and antiCsrfToken is not None:
                                        logger.info(f"获取token成功: {antiCsrfToken}")
                                        break
                                    time.sleep(0.2)
                                # logger.info(f"policyEntityId {policyEntityId} 详情: {detail}")
                                try:
                                    resolution_paths = detail.get("policies", [])[0].get("policyAttributeNameToTypeValue", {}).get("resolutionConfig", {}).get("attributeValue", {})
                                    if resolution_paths:
                                        resolution_paths = json.loads(resolution_paths)
                                        resolution_paths = resolution_paths.get("ResolutionPaths", [{}, {}, {}])[2].get("id", "")
                                    logger.info(resolution_paths)
                                    time.sleep(random.randint(1,3) * random.uniform(0.5, 1))
                                    # attribute_value = detail.get("policies", [])[0].get("policyAttributeNameToTypeValue", {}).get("resolutionConfig", {}).get("attributeValue",{}).get("ResolutionPaths",[{},{},{}])[2].get("id","")
                                    # logger.info(f"attributeValueId: {attribute_value}")
                                except Exception as e:
                                    logger.error(f"获取resolutionConfig.attributeValue出错: {e}")
                                # 构建js请求，判断是否存在可提交的按钮
                                headers = {
                                    "authority": domain,
                                    "accept": "*/*",
                                    "accept-language": "zh-TW,zh;q=0.9",
                                    "anti-csrftoken-a2z-request": "true",
                                    "Content-Type": "application/json",
                                    "origin": f"https://{domain}",
                                    "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                }
                                js_code = f"""
                                window._defectDetail = undefined;
                                fetch('https://{domain}/spx/myc/myc-backend-service/api/get-artifact-submission-batch', {{
                                    method: 'POST',
                                    headers: {json.dumps(headers)},
                                    body: JSON.stringify({{
                                        "contributor": {{"ContributorType": "SELLER"}},
                                        "policyId": {policyEntityId},
                                        "resolutionPathId": "{resolution_paths}",
                                        "entity": {{
                                            "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
                                            "values": {{"SKU": "{sku}"}}
                                        }},
                                        "artifactRequests": [{{
                                            "namespace": "contribution",
                                            "name": "gpsr_safety_attestation",
                                            "schemaSource": "UMP"
                                        }}]
                                    }})
                                }})
                                .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                """
                                self.page.run_js(js_code)
                                for _ in range(180):
                                    next_detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                    antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                    if next_detail is not None and antiCsrfToken is not None:
                                        logger.info(f"获取next_token成功: {antiCsrfToken}")
                                        break
                                    time.sleep(0.2)
                                logger.info(f"policyEntityId {policyEntityId} 提交详情: {next_detail}")
                                time.sleep(random.randint(1,3) * random.uniform(0.5, 1))
                                # 新增判断：如果 next_detail 为 None 且高危，直接写入数据库
                                if next_detail is None and has_product_safety_cert == 2:
                                    logger.info(f"policyEntityId {policyEntityId} 高危无提交按钮，直接写入数据库")
                                    insert_data["is_click"] = 2
                                    insert_data["data_status"] = 2
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert, data_status)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s, %(data_status)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert),
                                            data_status = VALUES(data_status)
                                    """, [insert_data])
                                elif next_detail is not None and next_detail.get("artifacts","") != "":
                                    logger.info(f"policyEntityId {policyEntityId} 为未提交的数据")
                                    headers = {
                                        "authority": domain,
                                        "accept": "*/*",
                                        "accept-language": "en,en-GB;q=0.9",
                                        "anti-csrftoken-a2z": antiCsrfToken,
                                        "content-type": "application/json",
                                        "origin": f"https://{domain}",
                                        "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                        "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                        "sec-ch-ua-mobile": "?0",
                                        "sec-ch-ua-platform": '"Windows"',
                                        "sec-fetch-dest": "empty",
                                        "sec-fetch-mode": "cors",
                                        "sec-fetch-site": "same-origin",
                                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                    }
                                    js_code = f"""
                                    window._defectDetail = undefined;
                                    fetch('https://{domain}/spx/myc/myc-backend-service/api/put-artifact-submission-batch', {{
                                        method: 'POST',
                                        headers: {json.dumps(headers)},
                                        body: JSON.stringify({{
                                            "contributor": {{"ContributorType": "SELLER", "ContributorValue": "{sellerId}"}},
                                            "policyId": {policyEntityId},
                                            "resolutionPathId": "{resolution_paths}",
                                            "entity": {{
                                                "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
                                                "values": {{"SKU": "{sku}"}}
                                            }},
                                            "artifacts": [{{
                                                "namespace": "contribution",
                                                "name": "gpsr_safety_attestation",
                                                "schemaSource": "UMP",
                                                "payload": "{{\\"value\\":true}}",
                                                "selectors": {{}}
                                            }}]
                                        }})
                                    }})
                                    .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                    """
                                    self.page.run_js(js_code)
                                    for _ in range(120):
                                        sumbit_detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                        antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                        if sumbit_detail is not None and antiCsrfToken is not None:
                                            logger.info(f"获取sumit_token成功: {antiCsrfToken}")
                                            break
                                        time.sleep(0.2)
                                    logger.info(f"policyEntityId {policyEntityId} 提交结果: {sumbit_detail}")
                                    clicked_sku_list.append(sku)
                                    insert_data["is_click"] = 1
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert)
                                    """, [insert_data])

                                else:
                                    logger.info(f"policyEntityId {policyEntityId} 为已提交的数据，跳过")
                                    insert_data["is_click"] = -1
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert)
                                    """, [insert_data])
                        except Exception as e:
                            logger.error(f"处理数据异常: {e}")
                            logger.error(f"异常数据: {loop_item}")
                            continue
                        time.sleep(random.randint(2, 3))

                    # 检查当前页面是否有有效数据
                    if valid_data_count == 0:
                        logger.info(f"当前页面没有有效数据，跳过了{skipped_count}条数据，继续翻页")
                    else:
                        logger.info(f"当前页面处理了{valid_data_count}条有效数据，跳过了{skipped_count}条数据")

                    # 如果defects = 25条，尝试点击下一页
                    if len(defects) == 25:
                        click_result = self.click_next_page()
                        if not click_result:
                            consecutive_failed_pages += 1
                            logger.warning(f"翻页失败，连续失败次数: {consecutive_failed_pages}")
                            if consecutive_failed_pages >= max_consecutive_failed_pages:
                                logger.error(f"连续{max_consecutive_failed_pages}次翻页失败，退出处理")
                                break
                        else:
                            consecutive_failed_pages = 0  # 重置失败计数器
                            logger.info("翻页成功，等待页面加载")
                            # 验证页面是否正常加载
                            if not self.wait_for_page_load():
                                consecutive_failed_pages += 1
                                logger.warning(f"页面加载异常，连续失败次数: {consecutive_failed_pages}")
                                if consecutive_failed_pages >= max_consecutive_failed_pages:
                                    logger.error(f"连续{max_consecutive_failed_pages}次页面加载失败，退出处理")
                                    break
                            else:
                                logger.info("页面加载成功，继续处理")
                    else:
                        logger.info("当前页数据量不足25条，已到最后一页")
                        break  # 没有 nextPageToken，退出监听循环
        exist_result = db.query("select asin,id from data_amazon_compliance where platform_account = %s and platform_site = %s AND is_click IN (-1,1)", (store_name, store_site))
        detailed_asins = []
        for exist_item in exist_result:
            if exist_item['asin'] not in current_asins:
                no_clicked_sku_list.append(exist_item['asin'])
                logger.info(f"已经处理完毕的asin: {exist_item['asin']}")
                detailed_asins.append({"id": exist_item['id'], "is_click": 0})
        if len(detailed_asins) > 0:
            db.batch_upsert("""
                UPDATE data_amazon_compliance
                SET is_click = 0
                WHERE id = %s
            """, [(item['id'],) for item in detailed_asins])


        db.close()
        self.page.listen.stop()


def job():
    # 店铺实例化


    try:
        task_config = TaskConfig()
        task_data = task_config.get_config()

        if not task_data:
            logger.error("任务配置为空")
            return
        # 获取任务配置
        logger.info("=====获取任务配置=====")
        logger.info(task_data)
        excel_task_path = task_data.get("TaskConfig", {}).get("task_file", "")
        superbrowser_company = task_data.get(
            "SuperBrowser", {}).get("company", "")
        superbrowser_username = task_data.get(
            "SuperBrowser", {}).get("account", "")
        superbrowser_password = task_data.get(
            "SuperBrowser", {}).get("password", "")
        superbrowser_path = task_data.get(
            "SuperBrowser", {}).get("browser_path", "")
        if not superbrowser_path:
            logger.error("紫鸟客户端路径未设置")
            return
        if not os.path.exists(superbrowser_path):
            logger.error(f"紫鸟客户端路径不存在: {superbrowser_path}")
            return
        if not os.path.exists(excel_task_path):
            logger.error(f"任务配置文件不存在: {excel_task_path}")
            return
        if not superbrowser_company or not superbrowser_username or not superbrowser_password:
            logger.error("紫鸟账号信息未设置或设置错误")
            return

        # 表格对象 实例化
        excel_handle = ExcelHandler(file_path=excel_task_path)

        client_path = Rf'{superbrowser_path}'  # 客户端程序starter.exe的路径

        logger.info("获取可用通信端口....")
        common_task = Common()
        while True:
            try:
                # 获取可用端口号
                socket_port = common_task.find_available_port()
                logger.info(f"可用端口--{socket_port}")

                user_info = {
                    "company": f"{superbrowser_company}",
                    "username": f"{superbrowser_username}",
                    "password": f"{superbrowser_password}"
                }

                # 实例化 superBrowser 类
                browser_manager = superBrowser(socket_port)
                # 终止紫鸟客户端已启动的进程
                browser_manager.kill_process()
                browser_manager.kill_main_process()
                logger.info(f"实例账号：-{user_info.get('username')}")
                logger.info("=====启动客户端=====")
                browser_manager.start_browser(client_path, socket_port)
                logger.info("=====更新内核=====")
                browser_manager.update_core(user_info, socket_port)
                break
            except Exception as e:
                logger.error(f"启动客户端失败: {e}")
                time.sleep(2)
                continue

        """获取店铺列表"""
        logger.info("=====获取店铺列表=====")
        browser_list = browser_manager.get_browser_list(user_info)
        # logger.info(browser_list)

        if browser_list is None or not browser_list:
            logger.error("店铺列表为空")
            return
        else:
            logger.info("店铺列表获取成功！")
            # 只保留 platform_id 为 1 的店铺
            browser_list = [b for b in browser_list if b.get("platform_id") == '1']

        # write_dict_list_to_xlsx_pandas(browser_list=browser_list)

        excel_row = 0

        result = excel_handle.read_sheet(sheet_name=0)
        # 定义全局店铺名称、站点名称、全局店铺数据对象、全局站点数据对象
        gloabl_super_browser_name = ""
        global_super_browser_site = ""
        global_super_browser_data = {}
        # 当前店铺名称
        current_store_name = ""
        global_site_dict = {}
        # site_map_dict = {'uk': '英国', 'us': '美国', 'fr': '法国',
        #     'es': '西班牙', 'it': '意大利', 'de': '德国'}
        global_page = ""
        logger.info("=====开始执行监管合规性任务=====")
        logger.info(result)
        if result:
            for row in result:
                excel_row += 1  # 在处理每行数据前先增加行号
                
                # 检查端口是否可用，不可用则重启客户端
                # 循环检测端口可用性，最多等待120秒，每10秒检测一次，不可用则重启客户端
                max_wait = 80
                waited = 0
                if not browser_manager.check_client_running(user_info, socket_port):
                    logger.info(f"端口 {socket_port} 超过{max_wait}秒仍不可用，重新启动客户端")
                    browser_manager.start_browser(client_path, socket_port)
                    time.sleep(5)
                while not browser_manager.check_client_running(user_info, socket_port):
                    socket_port = common_task.find_available_port()
                    logger.info(f"端口 {socket_port} 不可用，等待10秒后重试")
                    time.sleep(10)
                    waited += 10
                    if waited >= max_wait:
                        logger.info(f"端口 {socket_port} 超过{max_wait}秒仍不可用，重新启动客户端")
                        browser_manager.start_browser(client_path, socket_port)
                        waited = 0
                id = row.get("id")
                super_browser_name = row.get("店铺", "").strip()
                if not super_browser_name:
                    logger.error(f"紫鸟店铺名称不能为空！")
                    excel_handle.write_cell(
                        row=excel_row, col="执行结果", value="紫鸟店铺名称不能为空！", sheet_name="截图店铺信息", type="error")
                    continue

                platform_site = row.get("国家", "")
                if not platform_site:
                    logger.error(f"站点不能为空！")
                    excel_handle.write_cell(
                        row=excel_row, col="执行结果", value="站点不能为空！", sheet_name="截图店铺信息", type="error")
                    continue
                try:

                    if super_browser_name == "" or platform_site == "":
                        logger.error("紫鸟店铺名称或者站点有误！")
                        excel_handle.write_cell(
                            row=excel_row, col="执行结果", value="紫鸟店铺名称或者站点有误！", sheet_name="截图店铺信息", type="error")
                        continue
                    if gloabl_super_browser_name != "" and super_browser_name == gloabl_super_browser_name:
                        logger.info("店铺一致，无需切换")
                        super_browser_data = global_super_browser_data
                        site_dict = global_site_dict
                        page = global_page
                    else:
                        if gloabl_super_browser_name != "":
                            logger.info("店铺更新，关闭旧店铺")
                            browser_oauth = global_super_browser_data.get(
                                "browserOauth")
                            browser_manager.close_store(
                                user_info=user_info, browser_oauth=browser_oauth)

                        super_browser_data = common_task.find_dict_by_browser_name(
                            browser_list, super_browser_name)
                        if super_browser_data:
                            global_super_browser_data = super_browser_data

                            page = browser_manager.use_one_browser_run_task(
                                super_browser_data, user_info)
                            # if "邮箱" in super_browser_data.get("browserName"):
                            if not page:
                                gloabl_super_browser_name = ""
                                global_super_browser_site = ""
                                global_super_browser_data = {}
                                global_site_dict = {}
                                logger.info(f"该店铺只用于登录店铺邮箱，参数无效！")
                                excel_handle.write_cell(
                                    row=excel_row, col="执行结果", value="该店铺只用于登录店铺邮箱，店铺无效", sheet_name="截图店铺信息", type="error")
                                continue
                            if not page:
                                logger.error(f"店铺-{super_browser_name}-打开失败")
                                excel_handle.write_cell(
                                    row=excel_row, col="执行结果", value="店铺句柄获取异常！", sheet_name="截图店铺信息", type="error")
                                continue
                            global_page = page
                            domain = common_task.get_domain_of_existing_page(
                                url=page.url)

                            browser_sites = browser_manager.get_browser_site(
                                page=page, domain=domain)
                            if browser_sites:
                                site_dict = {}
                                for i in browser_sites.get("regionalAccounts", []):
                                    site_dict[i.get('label')] = {"mons_sel_dir_mcid": i.get("ids", {}).get("mons_sel_dir_mcid"), "mons_sel_mkid": i.get(
                                        "ids", {}).get("mons_sel_mkid"), "globalAccountId": i.get("globalAccountId")}
                                global_site_dict = site_dict
                                current_store_name = super_browser_data.get(
                                    "browserName", "")
                                # logger.info(site_dict)
                        else:
                            global_page = ""
                            gloabl_super_browser_name = ""
                            global_super_browser_site = ""
                            global_super_browser_data = {}
                            global_site_dict = {}
                            logger.error(f"{super_browser_name}-紫鸟账号内不存在改紫鸟店铺")
                            excel_handle.write_cell(
                                row=excel_row, col="执行结果", value="紫鸟账号内不存在改紫鸟店铺", sheet_name="截图店铺信息", type="error")
                            continue

                    if platform_site != global_super_browser_site or super_browser_name != gloabl_super_browser_name:
                        # map_site = site_map_dict.get(platform_site.lower(),"")
                        map_site = platform_site.lower()
                        browser_site_data = site_dict.get(map_site, {})
                        if browser_site_data == {}:
                            logger.error(
                                f"店铺不存在该站点-{super_browser_name}-{platform_site}-{map_site}")
                            excel_handle.write_cell(
                                row=excel_row, col="执行结果", value=f"店铺不存在该站点-{super_browser_name}-{platform_site}-{map_site}", sheet_name="截图店铺信息", type="error")
                            continue

                        else:
                            site_url = f"https:{domain}/home?mons_sel_dir_mcid={browser_site_data.get('mons_sel_dir_mcid')}&mons_sel_mkid={browser_site_data.get('mons_sel_mkid')}&mons_sel_dir_paid={browser_site_data.get('globalAccountId')}&ignore_selection_changed=true"
                            page.get(site_url)
                            try:
                                is_fukuang = page.ele(
                                    ".status-page-message", timeout=3)
                                if is_fukuang:
                                    error_text = is_fukuang.text.replace(
                                        '\\n', '')
                                    logger.error(
                                        f"店铺-{super_browser_name}-站点-{platform_site}-异常：{error_text}")
                                    excel_handle.write_cell(
                                        row=excel_row, col="执行结果", value=f"{error_text}", sheet_name="截图店铺信息", type="error")
                                    continue
                            except Exception:
                                pass
                            global_super_browser_site = platform_site
                    else:
                        # if gloabl_super_browser_name != "" and super_browser_name == gloabl_super_browser_name:
                        logger.info("站点一致，无需切换")
                    gloabl_super_browser_name = super_browser_name
                    if super_browser_data:
                        try:
                            logger.info(
                                f"店铺-{super_browser_name}-站点-{platform_site}")
                            task = Task(page=page, store=current_store_name,
                                        site=platform_site)
                            task.run()
                            excel_handle.write_cell(
                                row=excel_row, col="执行结果", value="任务执行成功", sheet_name="截图店铺信息", type=None)
                            logger.info(
                                f"任务执行成功-{super_browser_name}-{platform_site}")
                            # exit()
                        except Exception as e:
                            logger.error(f"任务执行异常-{e}")
                            excel_handle.write_cell(
                                row=excel_row, col="执行结果", value=f"任务执行异常：{e}", sheet_name="截图店铺信息", type="error")

                        # break
                    else:

                        logger.error(f"店铺{super_browser_name}不存在！")
                        excel_handle.write_cell(
                            row=excel_row, col="执行结果", value=f"店铺{super_browser_name}不存在！", sheet_name="截图店铺信息", type="error")
                        continue

                except Exception as e:
                    # update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '{e}' WHERE id = {id}"
                    # rows_affected = sql_connect.update_data(update_query)
                    logger.error(f"任务执行异常-{e}")
                    excel_handle.write_cell(
                        row=excel_row, col="执行结果", value=f"任务执行异常：{e}", sheet_name="截图店铺信息", type="error")
                    browser_manager.close_store(
                        user_info=user_info, browser_oauth=browser_oauth)
                    gloabl_super_browser_name = ""
                    global_super_browser_site = ""
                    global_super_browser_data = {}
                    global_site_dict = {}
            browser_manager.get_exit(user_info)
            logger.info(f"全部任务执行完毕")

    except Exception as e:
        logger.error(f"出错-{e}")


if __name__ == "__main__":
    job()
