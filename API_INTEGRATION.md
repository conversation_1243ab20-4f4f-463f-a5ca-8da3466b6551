# Amazon插件API集成说明

## 概述

本插件已集成服务器API功能，可以将Amazon合规数据自动发送到ERP服务器。数据采用批量处理机制，提高效率并减少服务器压力。

## API配置

### 服务器地址
- **基础URL**: `https://erpapi.yxyglobal.com`
- **接口端点**: `/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1`

### 数据表结构
- **表名**: `data_amazon_compliance`
- **数据库**: `rpa`
- **唯一键**: `unique_id`

## 功能特性

### 1. 批量数据处理
- **批次大小**: 50条数据/批次
- **最大等待时间**: 30秒
- **自动发送**: 当缓存达到批次大小或等待时间到达时自动发送

### 2. 数据缓存机制
- 数据首先保存到本地缓存
- 批量发送到服务器
- 失败时保留本地备份

### 3. 错误处理
- 网络错误自动重试
- 失败数据本地保存
- 详细错误日志记录

## 数据格式

### 发送到服务器的数据格式
```json
{
  "table_name": "data_amazon_compliance",
  "unique_key": ["unique_id"],
  "validate_fields": ["unique_id", "platform_account", "platform_site", "sku", "asin"],
  "database": "rpa",
  "data": [
    {
      "unique_id": "md5_hash_value",
      "platform_account": "店铺名称",
      "platform_site": "站点",
      "type": "消息类型",
      "asin": "ASIN",
      "sku": "SKU",
      "status": "状态",
      "platform_time": **********,
      "platform_end_time": **********,
      "create_time": **********,
      "brand": "品牌",
      "asin_num": 1,
      "sku_num": 1,
      "is_click": 1,
      "has_product_safety_cert": 1,
      "action_type": "submit_success",
      "submit_result": "{\"success\": true}",
      "processed_time": "2024-12-19T10:30:00.000Z"
    }
  ]
}
```

### 字段说明
- `unique_id`: 数据唯一标识（MD5哈希）
- `platform_account`: 平台账户名
- `platform_site`: 站点（如UK, US等）
- `type`: 消息类型
- `asin`: Amazon标准识别号
- `sku`: 库存单位
- `status`: 状态
- `platform_time`: 平台时间（时间戳）
- `platform_end_time`: 截止时间（时间戳）
- `create_time`: 创建时间（时间戳）
- `brand`: 品牌
- `asin_num`: ASIN数量
- `sku_num`: SKU数量
- `is_click`: 点击状态（-1: 已提交, 0: 未处理, 1: 提交成功, 2: 高危跳过）
- `has_product_safety_cert`: 产品安全认证等级（0: 无, 1: 中低风险, 2: 高风险）
- `action_type`: 操作类型（submit_success, already_submitted, high_risk_no_submit）
- `submit_result`: 提交结果（JSON字符串）
- `processed_time`: 处理时间（ISO格式）

## 使用方法

### 1. 自动模式
插件运行时会自动收集数据并发送到服务器，无需手动干预。

### 2. 手动控制
在插件状态窗口中提供了调试工具：
- **测试API**: 测试与服务器的连接
- **发送缓存**: 手动触发发送所有缓存数据
- **缓存状态**: 查看当前缓存中的数据数量

### 3. 消息接口
可以通过Chrome扩展消息系统与background script通信：

```javascript
// 测试API连接
chrome.runtime.sendMessage({ type: 'TEST_API' }, (response) => {
  console.log('API测试结果:', response);
});

// 手动发送缓存数据
chrome.runtime.sendMessage({ type: 'FLUSH_DATA' }, (response) => {
  console.log('发送结果:', response);
});

// 获取缓存状态
chrome.runtime.sendMessage({ type: 'GET_CACHE_STATUS' }, (response) => {
  console.log('缓存状态:', response);
});
```

## 测试工具

### API测试页面
项目根目录下的 `test_api.html` 文件提供了独立的API测试工具：
- 测试API连接
- 发送测试数据
- 自定义数据测试

### 使用方法
1. 在浏览器中打开 `test_api.html`
2. 点击"测试API连接"验证服务器连接
3. 使用"发送测试数据"测试批量数据发送
4. 通过"自定义数据测试"发送特定格式的数据

## 监控和日志

### 1. 控制台日志
- 所有API操作都会在浏览器控制台输出详细日志
- 包括发送状态、错误信息、响应数据等

### 2. 本地存储
- 成功发送的批次记录: `batch_success_*`
- 失败发送的批次记录: `batch_failed_*`
- 处理完成的统计: `process_complete_*`

### 3. 状态窗口
插件状态窗口实时显示：
- 处理进度
- 当前操作
- 统计信息
- 运行日志

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 确认服务器地址正确
   - 查看控制台错误信息

2. **数据发送失败**
   - 检查数据格式是否正确
   - 确认必填字段是否完整
   - 查看服务器响应错误信息

3. **缓存数据丢失**
   - 数据会自动保存到本地存储
   - 可以通过Chrome开发者工具查看存储内容
   - 失败的数据会保留在 `batch_failed_*` 记录中

### 调试步骤

1. 打开Chrome开发者工具
2. 查看Console标签页的日志输出
3. 检查Network标签页的API请求
4. 使用插件状态窗口的调试工具
5. 运行独立的API测试页面

## 配置选项

目前API配置是硬编码的，如需修改可以编辑以下文件：
- `src/utils/apiClient.js` - API客户端配置
- `src/background/index.js` - 批量处理配置

## 更新日志

### v1.0.0
- 集成ERP API接口
- 实现批量数据处理
- 添加错误处理和重试机制
- 提供调试工具和测试页面
- 完善日志记录和监控功能
