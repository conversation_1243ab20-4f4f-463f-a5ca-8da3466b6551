def run(self):
        common_task = Common()
        domain = common_task.get_domain_of_existing_page(url=self.page.url)
        logger.info(f"当前页面的域名是: {domain}")
        try:
            invaid_text_ele = self.page.ele("tag:div@@class=invalid-charge-method-container", timeout=3)
            if invaid_text_ele:
                h1_ele = invaid_text_ele.ele("tag:h1@@text()=付款方式无效", timeout=2)
                if h1_ele:
                    invaid_text = h1_ele.text
                    raise Exception(invaid_text)
        except Exception as e:
            logger.info(f"未检测到无效付款方式提示或查找元素异常: {e}")


        self.page.listen.set_targets(targets=True)
        self.page.listen.start(targets=True)
        self.page.get(
            f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance")

        # 数据总量
        total_num = ""
        current_index = 25
        normal_data_map = {}
        db = MySQLClient(
            host='*************',
            user='root',
            password='123456',
            database='rpa'
        )
        store_name = self.store
        store_site = self.site
        no_clicked_sku_list = []
        clicked_sku_list = []
        current_asins = []
        total_progress = 0  # 全局计数器，统计所有分页累计处理的defect条数
        consecutive_failed_pages = 0  # 连续翻页失败计数器
        max_consecutive_failed_pages = 3  # 最大连续翻页失败次数
        for packet in self.page.listen.steps():
            if packet.url and "performance/api/summary" in packet.url:
                total_num = packet.response.body.get("listingLevelMetrics", {}).get(
                    "REGULATORY_COMPLIANCE", {}).get("defects", {}).get("count", "")
                logger.info(f"数据总量: {total_num}")
                if total_num == 0:
                    logger.info("没有数据")
                    self.page.listen.stop()
                    return
                # elif total_num > 20000:
                #     logger.error("数据量过大，请手动下载")
                #     self.page.listen.stop()
                #     raise Exception("数据量大于5000，请手动下载")

            # print(packet.url)
            if packet.url and "zh-CN.json" in packet.url:
                for key, value in packet.response.body.items():
                    normal_data_map[key] = value
                # logger.info(f"normal_data_map: {normal_data_map}")
            if packet.url and "performance/api/product/policy/defects/pagination?metricNames=" in packet.url and "statuses=Open" in packet.url:
                sellerId = packet.response.body.get("sellerId", "")
                if not sellerId:
                    logger.error("未获取到sellerId")
                    self.page.listen.stop()
                    break
                defects = packet.response.body.get("defects", [])
                # logger.info(f"defects: {defects}")

                if not defects:
                    logger.info("没有产品政策数据")
                    # 继续尝试下一页，不停止处理
                    continue
                else:
                    logger.info("产品政策数据获取成功")
                    logger.info(f"当前数据量: {len(defects)}")
                    loop_index = 0
                    valid_data_count = 0  # 当前页面有效数据计数
                    skipped_count = 0  # 跳过数据计数
                    for loop_item in defects:
                        try:
                            loop_index = loop_index + 1
                            total_progress += 1
                            insert_data = {}
                            msg_type = loop_item.get("issueList",[])[0].get("parameters",{}).get("message_id","")
                            msg = normal_data_map.get(msg_type, "")
                            if msg_type != "sp_health_detail_table_reason_psi_details":
                                logger.info(f"跳过非政策详情数据: {msg_type} (进度: {total_progress}/{total_num}, 当前页面: {loop_index}/{len(defects)})")
                                skipped_count += 1
                                continue
                            valid_data_count += 1  # 找到有效数据，计数器加1
                            policyEntityId = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("policy_id", "")
                            sku = loop_item.get("issueList")[0].get("target",{}).get("artifactId","")
                            logger.info(f"处理数据: policyEntityId: {policyEntityId}, sku: {sku}, msg: {msg}")
                            if sku in clicked_sku_list:
                                logger.info(f"已处理过的SKU: {sku}, 跳过")
                                continue
                            asin = loop_item.get("issueList")[0].get("parameters",{}).get("asin","")
                            current_asins.append(asin)
                            start_time =  loop_item.get("issueList", [{}])[0].get("parameters", {}).get("impactDate")
                            due_time = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("due_date")
                            brand = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("brand", "")
                            skus,asins = self.get_num(loop_item.get("issueList"))
                            if start_time == "" or start_time is None:
                                start_time = 0
                            else:
                                # 将时间字符串 "2025年4月4日" 转换为秒级时间戳
                                try:
                                    # 先将中文日期转为标准日期字符串
                                    if "年" in start_time and "月" in start_time and "日" in start_time:
                                        year = int(start_time.split("年")[0])
                                        month = int(start_time.split("年")[1].split("月")[0])
                                        day = int(start_time.split("月")[1].split("日")[0])
                                        dt = time.strptime(f"{year}-{month:02d}-{day:02d}", "%Y-%m-%d")
                                        # 转为时间戳（假设为中国时区0点）
                                        start_time = int(time.mktime(dt))
                                    else:
                                        dt = time.strptime(start_time, "%Y-%m-%d")
                                        start_time = int(time.mktime(dt))
                                except Exception as e:
                                    logger.error(f"时间格式化出错: {e}")
                                    start_time = start_time
                            if due_time == "" or due_time is None:
                                due_time = 0
                            else:
                                due_time = self.convert_utc_to_cst(due_time)
                                # 将due_time字符串（格式为"%Y-%m-%d"）转换为中国时区的秒级时间戳
                                try:
                                    dt = time.strptime(due_time, "%Y-%m-%d")
                                    due_time = int(time.mktime(dt))
                                except Exception as e:
                                    logger.error(f"due_time格式化出错: {e}")
                                    due_time = due_time
                            # 显示任务进度
                            progress = loop_index

                            logger.info(f"任务进度: {total_progress}/{total_num} (当前页面: {loop_index}/{len(defects)})")
                            logger.info(f"store_name: {store_name}")
                            logger.info(f"store_site: {store_site}")
                            logger.info(f"msg: {msg}")
                            logger.info(f"asin: {loop_item.get('asin')}")
                            logger.info(f"sku: {sku}")
                            logger.info(f"status: {loop_item.get('status')}")
                            logger.info(f"start_time: {start_time}")
                            logger.info(f"due_time: {due_time}")
                            logger.info(store_name + store_site + msg + loop_item.get('asin') + sku + loop_item.get('status') + str(start_time) + str(due_time))
                            md5_value = self.md5_encrypt(store_name + store_site +msg + loop_item.get('asin') + sku + loop_item.get('status') + str(start_time)  + str(due_time))
                            logger.info(f"MD5值: {md5_value}")
                            query_sql = """SELECT * FROM data_amazon_compliance WHERE unique_id = %s AND is_click IN (-1, 1)
            """
                            query_exist = db.query(query_sql, (md5_value,))
                            if query_exist:
                                logger.info(f"{store_name}-{store_site}-已点击过的SKU: {sku}, 跳过")

                                continue
                            else:
                                logger.info(f"{store_name}-{store_site}-未点击过的SKU: {sku}, 继续处理")
                                    # # 批量 upsert 示例
                                current_timestamp = int(time.time())
                                logger.info(f"当前时间的秒级时间戳: {current_timestamp}")
                                data_sku = sku
                                timestamp = current_timestamp

                                # 获取 policy_name 用于判断风险等级
                                policy_name = loop_item.get("issueList", [{}])[0].get("parameters", {}).get("policy_name", "")

                                # 根据 policy_name 判断风险等级并设置 has_product_safety_cert
                                if policy_name == "GPSR_Reactive_HighRisk_PSI_DE":
                                    has_product_safety_cert = 2
                                elif policy_name == "GPSR_Reactive_MedLow-Risk_PSI_DE":
                                    has_product_safety_cert = 1
                                else:
                                    has_product_safety_cert = 0

                                insert_data = {
                                    "unique_id": md5_value,
                                    "platform_account": store_name,
                                    "platform_site": store_site,
                                    "type": msg,
                                    "asin": loop_item.get('asin', ""),
                                    "sku": data_sku,
                                    "status": loop_item.get('status'),
                                    "platform_time": start_time,
                                    "platform_end_time": due_time,
                                    "create_time": timestamp,
                                    "brand": brand,
                                    "asin_num": asins,
                                    "sku_num": skus,
                                    "is_click": 0,
                                    "has_product_safety_cert": has_product_safety_cert
                                }
                                if not sku:
                                    logger.error("sku为空，跳过")
                                    continue
                                if not policyEntityId:
                                    logger.error("policyEntityId为空，跳过")
                                    continue
                                logger.info(f"处理policyEntityId: {policyEntityId}")
                                # 构建js请求，取得绩效政策详情
                                headers = {
                                    "authority": domain,
                                    "accept": "*/*",
                                    "accept-language": "zh-TW,zh;q=0.9",
                                    "anti-csrftoken-a2z-request": "true",
                                    "Content-Type": "application/json",
                                    "origin": f"https://{domain}",
                                    "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                }
                                js_code = f"""
                                window._defectDetail = undefined;
                                fetch('https://{domain}/spx/myc/myc-backend-service/api/search-scpm-policies', {{
                                    method: 'POST',
                                    headers: {json.dumps(headers)},
                                    body: JSON.stringify({{"policyEntityId": {policyEntityId}}})
                                }})
                                .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                """
                                self.page.run_js(js_code)
                                for _ in range(120):
                                    detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                    antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                    if detail is not None and antiCsrfToken is not None:
                                        logger.info(f"获取token成功: {antiCsrfToken}")
                                        break
                                    time.sleep(0.2)
                                # logger.info(f"policyEntityId {policyEntityId} 详情: {detail}")
                                try:
                                    resolution_paths = detail.get("policies", [])[0].get("policyAttributeNameToTypeValue", {}).get("resolutionConfig", {}).get("attributeValue", {})
                                    if resolution_paths:
                                        resolution_paths = json.loads(resolution_paths)
                                        resolution_paths = resolution_paths.get("ResolutionPaths", [{}, {}, {}])[2].get("id", "")
                                    logger.info(resolution_paths)
                                    time.sleep(random.randint(1,3) * random.uniform(0.5, 1))
                                    # attribute_value = detail.get("policies", [])[0].get("policyAttributeNameToTypeValue", {}).get("resolutionConfig", {}).get("attributeValue",{}).get("ResolutionPaths",[{},{},{}])[2].get("id","")
                                    # logger.info(f"attributeValueId: {attribute_value}")
                                except Exception as e:
                                    logger.error(f"获取resolutionConfig.attributeValue出错: {e}")
                                # 构建js请求，判断是否存在可提交的按钮
                                headers = {
                                    "authority": domain,
                                    "accept": "*/*",
                                    "accept-language": "zh-TW,zh;q=0.9",
                                    "anti-csrftoken-a2z-request": "true",
                                    "Content-Type": "application/json",
                                    "origin": f"https://{domain}",
                                    "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                }
                                js_code = f"""
                                window._defectDetail = undefined;
                                fetch('https://{domain}/spx/myc/myc-backend-service/api/get-artifact-submission-batch', {{
                                    method: 'POST',
                                    headers: {json.dumps(headers)},
                                    body: JSON.stringify({{
                                        "contributor": {{"ContributorType": "SELLER"}},
                                        "policyId": {policyEntityId},
                                        "resolutionPathId": "{resolution_paths}",
                                        "entity": {{
                                            "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
                                            "values": {{"SKU": "{sku}"}}
                                        }},
                                        "artifactRequests": [{{
                                            "namespace": "contribution",
                                            "name": "gpsr_safety_attestation",
                                            "schemaSource": "UMP"
                                        }}]
                                    }})
                                }})
                                .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                """
                                self.page.run_js(js_code)
                                for _ in range(180):
                                    next_detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                    antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                    if next_detail is not None and antiCsrfToken is not None:
                                        logger.info(f"获取next_token成功: {antiCsrfToken}")
                                        break
                                    time.sleep(0.2)
                                logger.info(f"policyEntityId {policyEntityId} 提交详情: {next_detail}")
                                time.sleep(random.randint(1,3) * random.uniform(0.5, 1))
                                # 新增判断：如果 next_detail 为 None 且高危，直接写入数据库
                                if next_detail is None and has_product_safety_cert == 2:
                                    logger.info(f"policyEntityId {policyEntityId} 高危无提交按钮，直接写入数据库")
                                    insert_data["is_click"] = 2
                                    insert_data["data_status"] = 2
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert, data_status)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s, %(data_status)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert),
                                            data_status = VALUES(data_status)
                                    """, [insert_data])
                                elif next_detail is not None and next_detail.get("artifacts","") != "":
                                    logger.info(f"policyEntityId {policyEntityId} 为未提交的数据")
                                    headers = {
                                        "authority": domain,
                                        "accept": "*/*",
                                        "accept-language": "en,en-GB;q=0.9",
                                        "anti-csrftoken-a2z": antiCsrfToken,
                                        "content-type": "application/json",
                                        "origin": f"https://{domain}",
                                        "referer": f"https://{domain}/performance/account/health/product-policies?t=regulatory-compliance",
                                        "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                        "sec-ch-ua-mobile": "?0",
                                        "sec-ch-ua-platform": '"Windows"',
                                        "sec-fetch-dest": "empty",
                                        "sec-fetch-mode": "cors",
                                        "sec-fetch-site": "same-origin",
                                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                                    }
                                    js_code = f"""
                                    window._defectDetail = undefined;
                                    fetch('https://{domain}/spx/myc/myc-backend-service/api/put-artifact-submission-batch', {{
                                        method: 'POST',
                                        headers: {json.dumps(headers)},
                                        body: JSON.stringify({{
                                            "contributor": {{"ContributorType": "SELLER", "ContributorValue": "{sellerId}"}},
                                            "policyId": {policyEntityId},
                                            "resolutionPathId": "{resolution_paths}",
                                            "entity": {{
                                                "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
                                                "values": {{"SKU": "{sku}"}}
                                            }},
                                            "artifacts": [{{
                                                "namespace": "contribution",
                                                "name": "gpsr_safety_attestation",
                                                "schemaSource": "UMP",
                                                "payload": "{{\\"value\\":true}}",
                                                "selectors": {{}}
                                            }}]
                                        }})
                                    }})
                                    .then(async response => {{
                                    const data = await response.json();
                                    const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                                    window._defectDetail = {{
                                        data: data,
                                        antiCsrfToken: antiCsrfToken
                                    }};
                                }})
                                .catch(error => window._defectDetail = {{'error': error.toString()}});
                                    """
                                    self.page.run_js(js_code)
                                    for _ in range(120):
                                        sumbit_detail = self.page.run_js('return window._defectDetail ? window._defectDetail.data : null')
                                        antiCsrfToken = self.page.run_js('return window._defectDetail ? window._defectDetail.antiCsrfToken : null')
                                        if sumbit_detail is not None and antiCsrfToken is not None:
                                            logger.info(f"获取sumit_token成功: {antiCsrfToken}")
                                            break
                                        time.sleep(0.2)
                                    logger.info(f"policyEntityId {policyEntityId} 提交结果: {sumbit_detail}")
                                    clicked_sku_list.append(sku)
                                    insert_data["is_click"] = 1
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert)
                                    """, [insert_data])

                                else:
                                    logger.info(f"policyEntityId {policyEntityId} 为已提交的数据，跳过")
                                    insert_data["is_click"] = -1
                                    db.batch_upsert("""
                                        INSERT INTO data_amazon_compliance (unique_id, platform_account, platform_site, type, asin, sku, status, platform_time, platform_end_time, create_time, brand, asin_num, sku_num, is_click, has_product_safety_cert)
                                        VALUES (%(unique_id)s, %(platform_account)s, %(platform_site)s, %(type)s, %(asin)s, %(sku)s, %(status)s, %(platform_time)s, %(platform_end_time)s, %(create_time)s, %(brand)s, %(asin_num)s, %(sku_num)s, %(is_click)s, %(has_product_safety_cert)s)
                                        ON DUPLICATE KEY UPDATE
                                            platform_account = VALUES(platform_account),
                                            platform_site = VALUES(platform_site),
                                            type = VALUES(type),
                                            asin = VALUES(asin),
                                            sku = VALUES(sku),
                                            status = VALUES(status),
                                            platform_time = VALUES(platform_time),
                                            platform_end_time = VALUES(platform_end_time),
                                            create_time = VALUES(create_time),
                                            brand = VALUES(brand),
                                            asin_num = VALUES(asin_num),
                                            sku_num = VALUES(sku_num),
                                            is_click = VALUES(is_click),
                                            has_product_safety_cert = VALUES(has_product_safety_cert)
                                    """, [insert_data])
                        except Exception as e:
                            logger.error(f"处理数据异常: {e}")
                            logger.error(f"异常数据: {loop_item}")
                            continue
                        time.sleep(random.randint(2, 3))

                    # 检查当前页面是否有有效数据
                    if valid_data_count == 0:
                        logger.info(f"当前页面没有有效数据，跳过了{skipped_count}条数据，继续翻页")
                    else:
                        logger.info(f"当前页面处理了{valid_data_count}条有效数据，跳过了{skipped_count}条数据")

                    # 如果defects = 25条，尝试点击下一页
                    if len(defects) == 25:
                        click_result = self.click_next_page()
                        if not click_result:
                            consecutive_failed_pages += 1
                            logger.warning(f"翻页失败，连续失败次数: {consecutive_failed_pages}")
                            if consecutive_failed_pages >= max_consecutive_failed_pages:
                                logger.error(f"连续{max_consecutive_failed_pages}次翻页失败，退出处理")
                                break
                        else:
                            consecutive_failed_pages = 0  # 重置失败计数器
                            logger.info("翻页成功，等待页面加载")
                            # 验证页面是否正常加载
                            if not self.wait_for_page_load():
                                consecutive_failed_pages += 1
                                logger.warning(f"页面加载异常，连续失败次数: {consecutive_failed_pages}")
                                if consecutive_failed_pages >= max_consecutive_failed_pages:
                                    logger.error(f"连续{max_consecutive_failed_pages}次页面加载失败，退出处理")
                                    break
                            else:
                                logger.info("页面加载成功，继续处理")
                    else:
                        logger.info("当前页数据量不足25条，已到最后一页")
                        break  # 没有 nextPageToken，退出监听循环
        exist_result = db.query("select asin,id from data_amazon_compliance where platform_account = %s and platform_site = %s AND is_click IN (-1,1)", (store_name, store_site))
        detailed_asins = []
        for exist_item in exist_result:
            if exist_item['asin'] not in current_asins:
                no_clicked_sku_list.append(exist_item['asin'])
                logger.info(f"已经处理完毕的asin: {exist_item['asin']}")
                detailed_asins.append({"id": exist_item['id'], "is_click": 0})
        if len(detailed_asins) > 0:
            db.batch_upsert("""
                UPDATE data_amazon_compliance
                SET is_click = 0
                WHERE id = %s
            """, [(item['id'],) for item in detailed_asins])


        db.close()
        self.page.listen.stop()