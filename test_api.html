<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon插件API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🤖 Amazon插件API测试工具</h1>
    
    <div class="container">
        <h2>📊 API状态检查</h2>
        <button class="button" onclick="testApiConnection()">测试API连接</button>
        <button class="button" onclick="testWithProxy()">使用代理测试</button>
        <button class="button" onclick="networkDiagnosis()">网络诊断</button>
        <button class="button" onclick="getCacheStatus()">获取缓存状态</button>
        <button class="button" onclick="flushData()">强制发送缓存数据</button>
        <div class="form-group">
            <label>
                <input type="checkbox" id="useProxy" style="width: auto; margin-right: 5px;">
                使用CORS代理 (解决跨域问题)
            </label>
        </div>
        <div id="apiStatus"></div>
    </div>

    <div class="container">
        <h2>📤 测试数据发送</h2>
        <div class="form-group">
            <label for="testDataCount">测试数据数量:</label>
            <input type="number" id="testDataCount" value="5" min="1" max="100">
        </div>
        <button class="button" onclick="sendTestData()">发送测试数据</button>
        <div id="sendStatus"></div>
    </div>

    <div class="container">
        <h2>🔧 自定义数据测试</h2>
        <div class="form-group">
            <label for="customData">自定义JSON数据:</label>
            <textarea id="customData" placeholder='{"unique_id": "test_001", "platform_account": "test_store", "platform_site": "UK", "sku": "TEST-SKU-001", "asin": "B0TEST001", "type": "测试数据", "status": "Open", "platform_time": **********, "platform_end_time": **********, "create_time": **********, "brand": "TestBrand", "asin_num": 1, "sku_num": 1, "is_click": 0, "has_product_safety_cert": 1}'></textarea>
        </div>
        <button class="button" onclick="sendCustomData()">发送自定义数据</button>
        <div id="customStatus"></div>
    </div>

    <div class="container">
        <h2>📝 操作日志</h2>
        <button class="button" onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // API测试函数
        async function testApiConnection() {
            log('🔍 开始测试API连接...');
            showStatus('apiStatus', '正在测试API连接...', 'info');

            // 首先测试简单的GET请求
            try {
                log('📡 步骤1: 测试基础连接...');
                const pingResponse = await fetch('https://erpapi.yxyglobal.com/', {
                    method: 'GET',
                    mode: 'no-cors' // 避免CORS问题
                });
                log('✅ 基础连接成功');
            } catch (error) {
                log(`❌ 基础连接失败: ${error.message}`);
                showStatus('apiStatus', `网络连接失败: ${error.message}`, 'error');
                return;
            }

            // 测试API端点
            try {
                log('📡 步骤2: 测试API端点...');
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'connection_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'web_test_page'
                        }]
                    })
                });

                log(`📊 响应状态: ${response.status} ${response.statusText}`);
                log(`📊 响应头: ${JSON.stringify([...response.headers.entries()])}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ API连接测试成功: ${JSON.stringify(result)}`);
                showStatus('apiStatus', `API连接正常 - ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ API连接测试失败: ${error.message}`);
                log(`🔍 错误类型: ${error.constructor.name}`);
                log(`🔍 错误堆栈: ${error.stack}`);

                if (error.message.includes('CORS')) {
                    showStatus('apiStatus', `CORS跨域错误: ${error.message}<br><strong>解决方案:</strong> 请在Chrome扩展环境中测试，或配置服务器CORS策略`, 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    showStatus('apiStatus', `网络请求失败: ${error.message}<br><strong>可能原因:</strong> 1. 网络连接问题 2. 服务器不可达 3. CORS限制`, 'error');
                } else {
                    showStatus('apiStatus', `API连接失败: ${error.message}`, 'error');
                }
            }
        }

        // 使用CORS代理测试
        async function testWithProxy() {
            log('🔄 使用CORS代理测试API连接...');
            showStatus('apiStatus', '正在使用代理测试API连接...', 'info');

            try {
                // 使用公共CORS代理
                const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
                const targetUrl = 'https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1';

                log('📡 使用代理URL: ' + proxyUrl + targetUrl);

                const response = await fetch(proxyUrl + targetUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'proxy_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'web_test_page_proxy'
                        }]
                    })
                });

                log(`📊 代理响应状态: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ 代理API测试成功: ${JSON.stringify(result)}`);
                showStatus('apiStatus', `代理API连接正常 - ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ 代理API测试失败: ${error.message}`);
                showStatus('apiStatus', `代理API测试失败: ${error.message}<br><strong>提示:</strong> 可能需要先访问 https://cors-anywhere.herokuapp.com/corsdemo 启用代理`, 'error');
            }
        }

        // 网络诊断
        async function networkDiagnosis() {
            log('🔍 开始网络诊断...');
            showStatus('apiStatus', '正在进行网络诊断...', 'info');

            const tests = [
                {
                    name: '测试基础网络连接',
                    url: 'https://www.google.com',
                    method: 'GET',
                    mode: 'no-cors'
                },
                {
                    name: '测试目标服务器连接',
                    url: 'https://erpapi.yxyglobal.com',
                    method: 'GET',
                    mode: 'no-cors'
                },
                {
                    name: '测试API端点可达性',
                    url: 'https://erpapi.yxyglobal.com/api/v1/index/post',
                    method: 'GET',
                    mode: 'no-cors'
                }
            ];

            let results = [];

            for (let test of tests) {
                try {
                    log(`📡 ${test.name}...`);
                    const startTime = Date.now();

                    const response = await fetch(test.url, {
                        method: test.method,
                        mode: test.mode,
                        signal: AbortSignal.timeout(10000)
                    });

                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    results.push({
                        test: test.name,
                        status: 'success',
                        duration: duration + 'ms',
                        details: `状态: ${response.status || 'opaque'}`
                    });

                    log(`✅ ${test.name} - 成功 (${duration}ms)`);
                } catch (error) {
                    results.push({
                        test: test.name,
                        status: 'failed',
                        error: error.message
                    });

                    log(`❌ ${test.name} - 失败: ${error.message}`);
                }
            }

            // 显示诊断结果
            let resultHtml = '<h4>🔍 网络诊断结果:</h4><ul>';
            results.forEach(result => {
                if (result.status === 'success') {
                    resultHtml += `<li style="color: green;">✅ ${result.test} - ${result.duration} - ${result.details}</li>`;
                } else {
                    resultHtml += `<li style="color: red;">❌ ${result.test} - ${result.error}</li>`;
                }
            });
            resultHtml += '</ul>';

            // 添加建议
            resultHtml += '<h4>💡 建议:</h4><ul>';
            resultHtml += '<li>如果所有测试都失败，请检查网络连接</li>';
            resultHtml += '<li>如果只有API测试失败，可能是CORS限制，请使用Chrome扩展环境测试</li>';
            resultHtml += '<li>可以尝试使用代理模式进行测试</li>';
            resultHtml += '</ul>';

            showStatus('apiStatus', resultHtml, results.every(r => r.status === 'success') ? 'success' : 'error');
            log('🔍 网络诊断完成');
        }

        async function getCacheStatus() {
            log('📊 获取缓存状态...');
            // 这里应该通过chrome.runtime.sendMessage与background script通信
            // 但在测试页面中无法直接调用，所以显示模拟信息
            showStatus('apiStatus', '缓存状态: 这是测试页面，无法直接获取插件缓存状态', 'info');
            log('ℹ️ 注意: 此测试页面无法直接与插件通信，请在插件环境中测试');
        }

        async function flushData() {
            log('🚀 触发强制发送缓存数据...');
            showStatus('apiStatus', '强制发送: 这是测试页面，无法直接操作插件缓存', 'info');
            log('ℹ️ 注意: 此测试页面无法直接与插件通信，请在插件环境中测试');
        }

        async function sendTestData() {
            const count = parseInt(document.getElementById('testDataCount').value) || 5;
            const useProxy = document.getElementById('useProxy').checked;

            log(`📤 开始发送 ${count} 条测试数据${useProxy ? ' (使用代理)' : ''}...`);
            showStatus('sendStatus', `正在发送 ${count} 条测试数据...`, 'info');

            const testData = [];
            for (let i = 1; i <= count; i++) {
                testData.push({
                    unique_id: `test_${Date.now()}_${i}`,
                    platform_account: 'test_store',
                    platform_site: 'UK',
                    sku: `TEST-SKU-${String(i).padStart(3, '0')}`,
                    asin: `B0TEST${String(i).padStart(3, '0')}`,
                    type: '测试数据',
                    status: 'Open',
                    platform_time: Math.floor(Date.now() / 1000),
                    platform_end_time: Math.floor(Date.now() / 1000) + 86400,
                    create_time: Math.floor(Date.now() / 1000),
                    brand: 'TestBrand',
                    asin_num: 1,
                    sku_num: 1,
                    is_click: 0,
                    has_product_safety_cert: 1,
                    action_type: 'test_data',
                    processed_time: new Date().toISOString()
                });
            }

            try {
                const baseUrl = 'https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1';
                const url = useProxy ? 'https://cors-anywhere.herokuapp.com/' + baseUrl : baseUrl;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0',
                        ...(useProxy ? {'X-Requested-With': 'XMLHttpRequest'} : {})
                    },
                    body: JSON.stringify({
                        table_name: 'data_amazon_compliance',
                        unique_key: ['unique_id'],
                        validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
                        database: 'rpa',
                        data: testData
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log(`✅ 测试数据发送成功: ${JSON.stringify(result)}`);
                showStatus('sendStatus', `测试数据发送成功 - ${count} 条数据`, 'success');
            } catch (error) {
                log(`❌ 测试数据发送失败: ${error.message}`);
                showStatus('sendStatus', `测试数据发送失败: ${error.message}`, 'error');
            }
        }

        async function sendCustomData() {
            const customDataText = document.getElementById('customData').value.trim();
            if (!customDataText) {
                showStatus('customStatus', '请输入自定义数据', 'error');
                return;
            }
            
            try {
                const customData = JSON.parse(customDataText);
                log(`📤 发送自定义数据: ${JSON.stringify(customData)}`);
                showStatus('customStatus', '正在发送自定义数据...', 'info');
                
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0'
                    },
                    body: JSON.stringify({
                        table_name: 'data_amazon_compliance',
                        unique_key: ['unique_id'],
                        validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
                        database: 'rpa',
                        data: [customData]
                    })
                });
                
                const result = await response.json();
                log(`✅ 自定义数据发送成功: ${JSON.stringify(result)}`);
                showStatus('customStatus', `自定义数据发送成功`, 'success');
            } catch (error) {
                log(`❌ 自定义数据发送失败: ${error.message}`);
                showStatus('customStatus', `发送失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 API测试页面已加载');
            log('ℹ️ 这是一个独立的测试页面，用于测试API接口');
            log('ℹ️ 要测试完整的插件功能，请在Amazon页面中使用插件');
        });
    </script>
</body>
</html>
