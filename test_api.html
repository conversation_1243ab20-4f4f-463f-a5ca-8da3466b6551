<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon插件API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🤖 Amazon插件API测试工具</h1>
    
    <div class="container">
        <h2>📊 API状态检查</h2>
        <button class="button" onclick="testApiConnection()">测试API连接</button>
        <button class="button" onclick="getCacheStatus()">获取缓存状态</button>
        <button class="button" onclick="flushData()">强制发送缓存数据</button>
        <div id="apiStatus"></div>
    </div>

    <div class="container">
        <h2>📤 测试数据发送</h2>
        <div class="form-group">
            <label for="testDataCount">测试数据数量:</label>
            <input type="number" id="testDataCount" value="5" min="1" max="100">
        </div>
        <button class="button" onclick="sendTestData()">发送测试数据</button>
        <div id="sendStatus"></div>
    </div>

    <div class="container">
        <h2>🔧 自定义数据测试</h2>
        <div class="form-group">
            <label for="customData">自定义JSON数据:</label>
            <textarea id="customData" placeholder='{"unique_id": "test_001", "platform_account": "test_store", "platform_site": "UK", "sku": "TEST-SKU-001", "asin": "B0TEST001", "type": "测试数据", "status": "Open", "platform_time": **********, "platform_end_time": **********, "create_time": **********, "brand": "TestBrand", "asin_num": 1, "sku_num": 1, "is_click": 0, "has_product_safety_cert": 1}'></textarea>
        </div>
        <button class="button" onclick="sendCustomData()">发送自定义数据</button>
        <div id="customStatus"></div>
    </div>

    <div class="container">
        <h2>📝 操作日志</h2>
        <button class="button" onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // API测试函数
        async function testApiConnection() {
            log('🔍 开始测试API连接...');
            showStatus('apiStatus', '正在测试API连接...', 'info');
            
            try {
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'connection_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'web_test_page'
                        }]
                    })
                });
                
                const result = await response.json();
                log(`✅ API连接测试成功: ${JSON.stringify(result)}`);
                showStatus('apiStatus', `API连接正常 - ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ API连接测试失败: ${error.message}`);
                showStatus('apiStatus', `API连接失败: ${error.message}`, 'error');
            }
        }

        async function getCacheStatus() {
            log('📊 获取缓存状态...');
            // 这里应该通过chrome.runtime.sendMessage与background script通信
            // 但在测试页面中无法直接调用，所以显示模拟信息
            showStatus('apiStatus', '缓存状态: 这是测试页面，无法直接获取插件缓存状态', 'info');
            log('ℹ️ 注意: 此测试页面无法直接与插件通信，请在插件环境中测试');
        }

        async function flushData() {
            log('🚀 触发强制发送缓存数据...');
            showStatus('apiStatus', '强制发送: 这是测试页面，无法直接操作插件缓存', 'info');
            log('ℹ️ 注意: 此测试页面无法直接与插件通信，请在插件环境中测试');
        }

        async function sendTestData() {
            const count = parseInt(document.getElementById('testDataCount').value) || 5;
            log(`📤 开始发送 ${count} 条测试数据...`);
            showStatus('sendStatus', `正在发送 ${count} 条测试数据...`, 'info');
            
            const testData = [];
            for (let i = 1; i <= count; i++) {
                testData.push({
                    unique_id: `test_${Date.now()}_${i}`,
                    platform_account: 'test_store',
                    platform_site: 'UK',
                    sku: `TEST-SKU-${String(i).padStart(3, '0')}`,
                    asin: `B0TEST${String(i).padStart(3, '0')}`,
                    type: '测试数据',
                    status: 'Open',
                    platform_time: Math.floor(Date.now() / 1000),
                    platform_end_time: Math.floor(Date.now() / 1000) + 86400,
                    create_time: Math.floor(Date.now() / 1000),
                    brand: 'TestBrand',
                    asin_num: 1,
                    sku_num: 1,
                    is_click: 0,
                    has_product_safety_cert: 1,
                    action_type: 'test_data',
                    processed_time: new Date().toISOString()
                });
            }
            
            try {
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0'
                    },
                    body: JSON.stringify({
                        table_name: 'data_amazon_compliance',
                        unique_key: ['unique_id'],
                        validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
                        database: 'rpa',
                        data: testData
                    })
                });
                
                const result = await response.json();
                log(`✅ 测试数据发送成功: ${JSON.stringify(result)}`);
                showStatus('sendStatus', `测试数据发送成功 - ${count} 条数据`, 'success');
            } catch (error) {
                log(`❌ 测试数据发送失败: ${error.message}`);
                showStatus('sendStatus', `测试数据发送失败: ${error.message}`, 'error');
            }
        }

        async function sendCustomData() {
            const customDataText = document.getElementById('customData').value.trim();
            if (!customDataText) {
                showStatus('customStatus', '请输入自定义数据', 'error');
                return;
            }
            
            try {
                const customData = JSON.parse(customDataText);
                log(`📤 发送自定义数据: ${JSON.stringify(customData)}`);
                showStatus('customStatus', '正在发送自定义数据...', 'info');
                
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Amazon-Extension-Test/1.0'
                    },
                    body: JSON.stringify({
                        table_name: 'data_amazon_compliance',
                        unique_key: ['unique_id'],
                        validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
                        database: 'rpa',
                        data: [customData]
                    })
                });
                
                const result = await response.json();
                log(`✅ 自定义数据发送成功: ${JSON.stringify(result)}`);
                showStatus('customStatus', `自定义数据发送成功`, 'success');
            } catch (error) {
                log(`❌ 自定义数据发送失败: ${error.message}`);
                showStatus('customStatus', `发送失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 API测试页面已加载');
            log('ℹ️ 这是一个独立的测试页面，用于测试API接口');
            log('ℹ️ 要测试完整的插件功能，请在Amazon页面中使用插件');
        });
    </script>
</body>
</html>
