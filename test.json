url = https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1


请求示例
1
{
        "table_name": "test_data",
        "unique_key": ["order_no", "product_code"],
        "validate_fields": ["order_no", "product_code", "product_name"],
        "database": "rpa",
        "data": [
            {
                "order_no": "ORD20241219001",
                "product_code": "PROD001",
                "product_name": "测试产品1",
                "quantity": 10,
                "price": 99.99,
                "status": 1
            }
        ]
    }

// <?php
// // +----------------------------------------------------------------------
// // | 通用数据转发控制器
// // +----------------------------------------------------------------------
// // | 支持通用数据写入数据库接口，根据唯一键或验证字段更新数据
// // +----------------------------------------------------------------------
// // | Author: AI Assistant
// // +----------------------------------------------------------------------

// namespace app\api\service\rpa;

// use app\api\service\Base;
// use app\common\service\BatchSql;
// use think\Db;

// class Data extends Base
// {
//     /**
//      * /api/v1/index/post?c=rpa/data&a=saveData
//      * Desc: 通用数据处理接口（支持插入和更新）
//      * Author: AI Assistant
//      * Date: 2024/12/19
//      * Updated: 2025/08/06
//      * @method post
//      * @param string table_name 表名
//      * @param string unique_key 表唯一键字段名
//      * @param array validate_fields 数据验证字段
//      * @param array data 要处理的数据
//      * @param string database 数据库名（可选，默认rpa）
//      */
//     public function saveData()
//     {
//         $params = $this->requestParams ?? [];
//         if (empty($params['table_name'])) {
//             return $this->error('数据表不能为空');
//         }
//         if (empty($params['unique_key'])) {
//             return $this->error('唯一key组成不能为空');
//         }
//         // 参数验证
//         if (empty($params['data'])) {
//             return $this->error('数据不能为空');
//         }
        
//         $table_name = $params['table_name'];
//         $unique_key_arr = is_array($params['unique_key']) ? $params['unique_key'] : [$params['unique_key']];
//         $validate_fields = $params['validate_fields'] ?? [];
//         $database = $params['database'] ?? 'rpa'; // 默认使用rpa数据库
//         $data = $params['data'];
        
//         // 验证表是否存在
//         $table_exists = $this->checkTableExists($table_name, $database);
//         if (empty($table_exists)) {
//             return $this->error("表 {$table_name} 不存在");
//         }

//         $where = [];
//         foreach ($unique_key_arr as $field) {
//             $where[$field] = ['in', array_unique(array_column($data, $field))];
//         }
//         $list = Db::connect('rpa')->table("{$database}.{$table_name}")->where($where)->select();
//         $existing_record_map = [];
        
//         foreach ($list as $value) {
//             $key = "";
//             foreach ($unique_key_arr as $field) {
//                 $key .= $value[$field] . '_';
//             }
//             $key = trim($key, '_');
//             $existing_record_map[$key] = $value;
//         }

//         $update_params = [];
//         $insert_params = [];
//         foreach ($data as &$value) {
//             $key = "";
//             foreach ($unique_key_arr as $field) {
//                 $key .= $value[$field] . '_';
//             }
//             $key = trim($key, '_');
//             if (empty($value['unique_id'])) {
//                 $value['unique_id'] = md5($key);
//             }

//             if (isset($existing_record_map[$key])) {
//                 $value['id'] = $existing_record_map[$key]['id'];
//                 $update_params[] = $value;
//             } else {
//                 $insert_params[] = $value;
//             }
//         }
//         unset($value);

//         if (!empty($update_params)) {
//             $update_params = array_chunk($update_params, 50);
//             foreach ($update_params as $value) {
//                 $sql = BatchSql::getInstance()->batchUpdate($value, 'id', [], "{$table_name}");
//                 $result = Db::connect('rpa')->table("{$database}.{$table_name}")->execute($sql);
//                 if ($result === false) {
//                     return $this->error('批量插入失败');
//                 }
//             }
//         }

//         if (!empty($insert_params)) {
//             $insert_params = array_chunk($insert_params, 50);
//             foreach ($insert_params as $value) {
//                 $sql = BatchSql::getInstance()->batchInsert("{$database}.{$table_name}", $value);
//                 $result = Db::connect('rpa')->execute($sql);
//                 if ($result === false) {
//                     return $this->error('批量插入失败');
//                 }
//             }
//         }

//         return $this->success('数据处理成功');
//     }
    
//     /**
//      * 检查表是否存在
//      * @param string $table_name 表名
//      * @param string $database 数据库名
//      * @return bool
//      */
//     private function checkTableExists($table_name, $database)
//     {
//         try {
//             $result = Db::connect('rpa')->query("SELECT table_name FROM information_schema.tables WHERE table_schema = '{$database}' AND table_name = '{$table_name}';");
//             return !empty($result);
//         } catch (\Exception $e) {
//             return false;
//         }
//     }
// }
