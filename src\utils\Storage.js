// 存储工具模块
import { Message } from './Message.js';

/**
 * 存储配置
 */
const STORAGE_CONFIG = {
  DEFAULT_TTL: 24 * 60 * 60 * 1000, // 24小时
  MAX_ITEMS: 1000,
  CLEANUP_INTERVAL: 60 * 60 * 1000 // 1小时
};

/**
 * 保存数据到本地存储
 * @param {string} key - 存储键
 * @param {any} value - 存储值
 * @param {number} ttl - 生存时间（毫秒）
 * @returns {Promise<void>}
 */
export async function saveToStorage(key, value, ttl = STORAGE_CONFIG.DEFAULT_TTL) {
  try {
    const data = {
      value,
      timestamp: Date.now(),
      ttl: ttl,
      expiresAt: Date.now() + ttl
    };
    
    await chrome.storage.local.set({ [key]: data });
    console.log(`💾 Data saved to storage: ${key}`);
  } catch (error) {
    console.error(`❌ Failed to save data to storage: ${key}`, error);
    throw error;
  }
}

/**
 * 从本地存储获取数据
 * @param {string} key - 存储键
 * @param {any} defaultValue - 默认值
 * @returns {Promise<any>}
 */
export async function getFromStorage(key, defaultValue = null) {
  try {
    const result = await chrome.storage.local.get([key]);
    const data = result[key];
    
    if (!data) {
      return defaultValue;
    }
    
    // 检查是否过期
    if (data.expiresAt && Date.now() > data.expiresAt) {
      console.log(`⏰ Data expired: ${key}`);
      await removeFromStorage(key);
      return defaultValue;
    }
    
    return data.value;
  } catch (error) {
    console.error(`❌ Failed to get data from storage: ${key}`, error);
    return defaultValue;
  }
}

/**
 * 从本地存储删除数据
 * @param {string} key - 存储键
 * @returns {Promise<void>}
 */
export async function removeFromStorage(key) {
  try {
    await chrome.storage.local.remove([key]);
    console.log(`🗑️ Data removed from storage: ${key}`);
  } catch (error) {
    console.error(`❌ Failed to remove data from storage: ${key}`, error);
    throw error;
  }
}

/**
 * 清空所有本地存储
 * @returns {Promise<void>}
 */
export async function clearStorage() {
  try {
    await chrome.storage.local.clear();
    console.log('🗑️ All storage cleared');
  } catch (error) {
    console.error('❌ Failed to clear storage', error);
    throw error;
  }
}

/**
 * 获取存储使用情况
 * @returns {Promise<Object>}
 */
export async function getStorageInfo() {
  try {
    const data = await chrome.storage.local.get(null);
    const keys = Object.keys(data);
    
    return {
      totalKeys: keys.length,
      keys: keys,
      estimatedSize: JSON.stringify(data).length
    };
  } catch (error) {
    console.error('❌ Failed to get storage info', error);
    return { totalKeys: 0, keys: [], estimatedSize: 0 };
  }
}

/**
 * 批量保存数据
 * @param {Object} dataObject - 要保存的数据对象
 * @param {number} ttl - 生存时间（毫秒）
 * @returns {Promise<void>}
 */
export async function saveBatchToStorage(dataObject, ttl = STORAGE_CONFIG.DEFAULT_TTL) {
  try {
    const storageData = {};
    
    for (const [key, value] of Object.entries(dataObject)) {
      storageData[key] = {
        value,
        timestamp: Date.now(),
        ttl: ttl,
        expiresAt: Date.now() + ttl
      };
    }
    
    await chrome.storage.local.set(storageData);
    console.log(`💾 Batch data saved to storage: ${Object.keys(dataObject).length} items`);
  } catch (error) {
    console.error('❌ Failed to save batch data to storage', error);
    throw error;
  }
}

/**
 * 批量获取数据
 * @param {Array<string>} keys - 存储键数组
 * @returns {Promise<Object>}
 */
export async function getBatchFromStorage(keys) {
  try {
    const result = await chrome.storage.local.get(keys);
    const cleanedResult = {};
    
    for (const [key, data] of Object.entries(result)) {
      if (data && data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Data expired: ${key}`);
        await removeFromStorage(key);
        continue;
      }
      
      cleanedResult[key] = data ? data.value : null;
    }
    
    return cleanedResult;
  } catch (error) {
    console.error('❌ Failed to get batch data from storage', error);
    return {};
  }
}

/**
 * 清理过期的存储数据
 * @returns {Promise<number>} 清理的数据项数量
 */
export async function cleanupExpiredStorage() {
  try {
    const data = await chrome.storage.local.get(null);
    const expiredKeys = [];
    
    for (const [key, item] of Object.entries(data)) {
      if (item && item.expiresAt && Date.now() > item.expiresAt) {
        expiredKeys.push(key);
      }
    }
    
    if (expiredKeys.length > 0) {
      await chrome.storage.local.remove(expiredKeys);
      console.log(`🧹 Cleaned up ${expiredKeys.length} expired items`);
    }
    
    return expiredKeys.length;
  } catch (error) {
    console.error('❌ Failed to cleanup expired storage', error);
    return 0;
  }
}

/**
 * 设置存储监听器
 */
export function setupStorageListener() {
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
      console.log('📊 Storage changed:', changes);
      
      // 可以在这里添加存储变化的处理逻辑
      for (const [key, change] of Object.entries(changes)) {
        console.log(`🔄 ${key}: ${change.oldValue ? 'updated' : 'added'}`);
      }
    }
  });
}

/**
 * 初始化存储系统
 */
export async function initializeStorage() {
  try {
    // 设置存储监听器
    setupStorageListener();
    
    // 清理过期数据
    await cleanupExpiredStorage();
    
    // 设置定期清理
    setInterval(cleanupExpiredStorage, STORAGE_CONFIG.CLEANUP_INTERVAL);
    
    console.log('✅ Storage system initialized');
  } catch (error) {
    console.error('❌ Failed to initialize storage system', error);
  }
}





