// 导入模块
import { saveToStorage, getFromStorage, initializeStorage } from '../utils/storage.js';
import configManager from '../utils/config.js';
import { saveAmazonComplianceData, batchSaveAmazonComplianceData, testApiConnection } from '../utils/apiClient.js';

// 数据缓存和批量发送管理
class DataBatchManager {
  constructor() {
    this.dataCache = [];
    this.batchSize = 50;
    this.maxWaitTime = 60000; // 60秒（适应海外网络）
    this.batchTimer = null;
    this.isProcessing = false;
    this.retryAttempts = 3; // 重试次数
    this.retryDelay = 5000; // 重试延迟5秒
  }

  // 添加数据到缓存
  addData(data) {
    this.dataCache.push({
      ...data,
      cached_time: new Date().toISOString()
    });

    console.log(`📦 数据已添加到缓存，当前缓存数量: ${this.dataCache.length}`);

    // 检查是否需要立即发送
    if (this.dataCache.length >= this.batchSize) {
      this.processBatch();
    } else {
      // 设置定时器，确保数据不会等待太久
      this.resetBatchTimer();
    }
  }

  // 重置批次定时器
  resetBatchTimer() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(() => {
      if (this.dataCache.length > 0) {
        this.processBatch();
      }
    }, this.maxWaitTime);
  }

  // 处理批次数据
  async processBatch() {
    if (this.isProcessing || this.dataCache.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const batchData = [...this.dataCache];
      this.dataCache = []; // 清空缓存

      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
      }

      console.log(`🚀 开始处理批次数据，数量: ${batchData.length}`);

      // 发送到服务器（带重试机制）
      const result = await this.sendWithRetry(batchData);

      if (result.success !== false) {
        console.log(`✅ 批次数据发送成功: ${batchData.length} 条`);

        // 保存成功记录到本地存储
        await saveToStorage(`batch_success_${Date.now()}`, {
          count: batchData.length,
          timestamp: new Date().toISOString(),
          result: result
        });
      } else {
        console.error(`❌ 批次数据发送失败:`, result);

        // 保存失败记录到本地存储
        await saveToStorage(`batch_failed_${Date.now()}`, {
          count: batchData.length,
          timestamp: new Date().toISOString(),
          error: result.error,
          data: batchData,
          retryAttempts: result.retryAttempts || 0
        });
      }
    } catch (error) {
      console.error('❌ 处理批次数据异常:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // 带重试机制的发送方法
  async sendWithRetry(batchData, attempt = 1) {
    try {
      console.log(`📡 发送尝试 ${attempt}/${this.retryAttempts}，数据量: ${batchData.length}`);
      const result = await saveAmazonComplianceData(batchData);
      return { ...result, retryAttempts: attempt };
    } catch (error) {
      console.error(`❌ 第 ${attempt} 次发送失败:`, error.message);

      if (attempt < this.retryAttempts) {
        console.log(`⏳ ${this.retryDelay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        return await this.sendWithRetry(batchData, attempt + 1);
      } else {
        console.error(`❌ 重试 ${this.retryAttempts} 次后仍然失败`);
        return {
          success: false,
          error: error.message,
          retryAttempts: attempt
        };
      }
    }
  }

  // 强制发送所有缓存数据
  async flushAll() {
    if (this.dataCache.length > 0) {
      await this.processBatch();
    }
  }
}

// 创建全局数据批次管理器
const dataBatchManager = new DataBatchManager();

// 初始化存储系统和配置
async function initializeSystems() {
  try {
    await initializeStorage();
    await configManager.initialize();

    console.log('✅ All systems initialized successfully');

    // 验证配置
    const validation = configManager.validate();
    if (!validation.valid) {
      console.error('❌ Configuration validation failed:', validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.warn('⚠️ Configuration warnings:', validation.warnings);
    }

    // 测试API连接
    try {
      const apiConnected = await testApiConnection();
      if (apiConnected) {
        console.log('✅ API连接测试成功');
      } else {
        console.warn('⚠️ API连接测试失败，数据将仅保存到本地');
      }
    } catch (error) {
      console.warn('⚠️ API连接测试异常:', error);
    }
  } catch (error) {
    console.error('❌ Failed to initialize systems:', error);
  }
}

// 初始化系统
initializeSystems();

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('🔔 Background script received message:', message);
  console.log('📤 Sender:', sender);
  
  if (message.type === 'EXECUTE_SCRIPT') {
    // 处理脚本执行请求
    (async () => {
      try {
        // 获取发送消息的标签页ID
        const tabId = sender.tab?.id;
        if (!tabId) {
          sendResponse({ error: '无法获取标签页ID' });
          return;
        }
        
        // 使用chrome.scripting.executeScript执行代码
        const results = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: (url, method, headers, body) => {
            // 这个函数完全在页面上下文中执行
            return new Promise((resolve, reject) => {
              try {
                // 执行fetch请求
                fetch(url, {
                  method: method,
                  headers: headers,
                  ...(body ? {body: JSON.stringify(body)} : {})
                })
                .then(response => {
                  // 获取anti-csrf-token
                  const antiCsrfToken = response.headers.get('Anti-Csrftoken-A2z');
                  return response.json().then(data => ({
                    data: data,
                    antiCsrfToken: antiCsrfToken
                  }));
                })
                .then(({data, antiCsrfToken}) => {
                  resolve({
                    data: data,
                    antiCsrfToken: antiCsrfToken
                  });
                })
                .catch(error => {
                  reject({
                    error: error.toString()
                  });
                });
              } catch (error) {
                reject({
                  error: error.toString()
                });
              }
            });
          },
          args: [message.url, message.method, message.headers, message.body]
        });

        // 处理执行结果
        if (results && results[0]) {
          const result = results[0].result;
          console.log('🔧 API响应:', result);
          sendResponse(result);
        } else {
          console.error('❌ 没有获取到执行结果');
          sendResponse({ error: '没有获取到执行结果' });
        }
      } catch (error) {
        console.error('❌ Script execution failed:', error);
        sendResponse({ error: error.message });
      }
    })();
    
    // 返回true表示异步响应
    return true;
  } else if (message.type === 'CLICK_NEXT_PAGE') {
    // 处理点击下一页请求
    (async () => {
      try {
        // 获取发送消息的标签页ID
        const tabId = sender.tab?.id;
        if (!tabId) {
          sendResponse({ success: false, error: '无法获取标签页ID' });
          return;
        }
        
        // 使用chrome.scripting.executeScript执行点击下一页操作
        const results = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: () => {
            // 使用shadow DOM查找翻页按钮（基于Python版本的逻辑）
            const katPagination = document.querySelector('kat-pagination#ahd-pp-pagination-katal-control');
            if (katPagination && katPagination.shadowRoot) {
              const shadow = katPagination.shadowRoot;
              const nextPageBtn = shadow.querySelector('span[part="pagination-nav-right"].nav.item');
              if (nextPageBtn) {
                nextPageBtn.click();
                return true;
              }
            }

            // 备用方案：尝试其他可能的翻页按钮选择器
            const fallbackSelectors = [
              '[data-testid="next-page-button"]',
              '.pagination-next',
              '[aria-label*="next"]',
              'button[aria-label*="Next"]',
              '.next-page'
            ];

            for (const selector of fallbackSelectors) {
              const nextButton = document.querySelector(selector);
              if (nextButton && !nextButton.disabled) {
                nextButton.click();
                return true;
              }
            }

            return false;
          }
        });
        
        // 返回执行结果
        const success = results && results[0] && results[0].result;
        sendResponse({ success: success });
      } catch (error) {
        console.error('❌ Click next page failed:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    
    // 返回true表示异步响应
    return true;
  } else if (message.type === 'WAIT_FOR_PAGE_LOAD') {
    // 处理等待页面加载请求
    (async () => {
      try {
        // 获取发送消息的标签页ID
        const tabId = sender.tab?.id;
        if (!tabId) {
          sendResponse({ loaded: true, error: '无法获取标签页ID' });
          return;
        }
        
        // 等待页面加载的逻辑
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 使用chrome.scripting.executeScript检查页面是否正常加载
        const results = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: () => {
            // 检查Amazon页面的关键元素
            const hasContent = document.querySelector('[data-testid="defects-table"]') ||
                              document.querySelector('.defects-container') ||
                              document.querySelector('kat-pagination#ahd-pp-pagination-katal-control') ||
                              document.querySelector('[data-testid="account-health-dashboard"]') ||
                              document.body.textContent.includes('defects') ||
                              document.body.textContent.includes('Product Policy') ||
                              document.body.textContent.includes('Regulatory Compliance');
            return !!hasContent;
          }
        });
        
        // 返回执行结果
        const loaded = results && results[0] && results[0].result;
        sendResponse({ loaded: loaded });
      } catch (error) {
        console.error('❌ Wait for page load failed:', error);
        sendResponse({ loaded: true, error: error.message }); // 默认认为加载成功，避免中断流程
      }
    })();
    
    // 返回true表示异步响应
    return true;
  } else if (message.type === 'defects-pagination' || message.type === 'AMAZON_API_RESPONSE') {
    console.log('=== Amazon Defects Pagination API Response ===');
    console.log('URL:', message.url);
    console.log('Raw Data:', message.data);
    
    // 简单保存原始数据
    const timestamp = new Date().toISOString();
    const dataKey = `amazon_defects_data_${timestamp}`;
    
    saveToStorage(dataKey, {
      url: message.url,
      data: message.data,
      timestamp: timestamp
    }).then(() => {
      console.log('💾 Defects数据已保存');
    }).catch(error => {
      console.error('❌ 保存defects数据失败:', error);
    });
    
    // 发送响应确认
    sendResponse({ 
      status: 'success', 
      message: 'Defects数据已接收并保存'
    });
    
  } else if (message.type === 'summary') {
    console.log('=== Amazon Summary API Response ===');
    console.log('URL:', message.url);
    console.log('Total Count:', message.totalCount);
    console.log('Raw Data:', message.data);
    
    // 保存summary数据
    const timestamp = new Date().toISOString();
    const dataKey = `amazon_summary_data_${timestamp}`;
    
    saveToStorage(dataKey, {
      url: message.url,
      data: message.data,
      totalCount: message.totalCount,
      timestamp: timestamp
    }).then(() => {
      console.log('💾 Summary数据已保存');
    }).catch(error => {
      console.error('❌ 保存summary数据失败:', error);
    });
    
    // 发送响应确认
    sendResponse({ 
      status: 'success', 
      message: `Summary数据已接收并保存，总数据量: ${message.totalCount}`
    });
    
  } else if (message.type === 'DATABASE_INSERT') {
    console.log('=== Database Insert Request ===');
    console.log('Action:', message.action);
    console.log('Data:', message.data);

    // 处理不同类型的数据库插入
    let processedData = { ...message.data };

    switch (message.action) {
      case 'high_risk_no_submit':
        console.log('🚨 高危无提交按钮数据:', message.data);
        processedData.action_type = 'high_risk_no_submit';
        processedData.submit_result = null;
        break;
      case 'submit_success':
        console.log('✅ 提交成功数据:', message.data);
        console.log('提交结果:', message.submitResult);
        processedData.action_type = 'submit_success';
        processedData.submit_result = JSON.stringify(message.submitResult || {});
        break;
      case 'already_submitted':
        console.log('📋 已提交数据:', message.data);
        processedData.action_type = 'already_submitted';
        processedData.submit_result = null;
        break;
      default:
        console.log('❓ 未知的数据库操作类型:', message.action);
        processedData.action_type = 'unknown';
        processedData.submit_result = null;
    }

    // 添加处理时间戳
    processedData.processed_time = new Date().toISOString();

    // 保存到本地存储（备份）
    const timestamp = new Date().toISOString();
    const dataKey = `db_insert_${message.action}_${timestamp}`;

    saveToStorage(dataKey, {
      action: message.action,
      data: processedData,
      submitResult: message.submitResult,
      timestamp: timestamp
    }).then(() => {
      console.log('💾 数据库插入数据已保存到本地存储');
    }).catch(error => {
      console.error('❌ 保存数据库插入数据失败:', error);
    });

    // 添加到批次管理器，准备发送到服务器
    try {
      dataBatchManager.addData(processedData);
      console.log('📦 数据已添加到批次管理器');
    } catch (error) {
      console.error('❌ 添加数据到批次管理器失败:', error);
    }

    // 发送响应确认
    sendResponse({
      status: 'success',
      message: `数据库插入操作已处理: ${message.action}`,
      cached: true
    });
    
  } else if (message.type === 'PROCESS_COMPLETE') {
    console.log('=== Process Complete ===');
    console.log('Message:', message.message);
    console.log('Statistics:', message.statistics);

    // 强制发送所有缓存的数据
    (async () => {
      try {
        console.log('🚀 处理完成，强制发送所有缓存数据...');
        await dataBatchManager.flushAll();
        console.log('✅ 所有缓存数据已发送完成');

        // 保存处理完成的统计信息
        await saveToStorage(`process_complete_${Date.now()}`, {
          message: message.message,
          statistics: message.statistics,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('❌ 发送缓存数据失败:', error);
      }
    })();

    // 发送响应确认
    sendResponse({
      status: 'success',
      message: '处理完成，数据已发送到服务器'
    });
    
  } else if (message.type === 'GET_CONFIG') {
    // 处理配置获取请求
    const config = configManager.getAll();
    sendResponse({ status: 'success', config });
    
  } else if (message.type === 'SET_CONFIG') {
    // 处理配置设置请求
    const { key, value } = message;
    configManager.set(key, value).then(() => {
      sendResponse({ status: 'success' });
    }).catch((error) => {
      sendResponse({ status: 'error', error: error.message });
    });
    return true; // 异步响应

  } else if (message.type === 'FLUSH_DATA') {
    // 手动触发数据发送
    console.log('=== Manual Data Flush Request ===');
    (async () => {
      try {
        console.log('🚀 手动触发数据发送...');
        await dataBatchManager.flushAll();
        console.log('✅ 手动数据发送完成');
        sendResponse({
          status: 'success',
          message: '数据发送完成',
          cacheSize: dataBatchManager.dataCache.length
        });
      } catch (error) {
        console.error('❌ 手动数据发送失败:', error);
        sendResponse({
          status: 'error',
          error: error.message
        });
      }
    })();
    return true; // 异步响应

  } else if (message.type === 'GET_CACHE_STATUS') {
    // 获取缓存状态
    console.log('=== Cache Status Request ===');
    sendResponse({
      status: 'success',
      cacheSize: dataBatchManager.dataCache.length,
      isProcessing: dataBatchManager.isProcessing,
      batchSize: dataBatchManager.batchSize
    });

  } else if (message.type === 'TEST_API') {
    // 测试API连接
    console.log('=== API Test Request ===');
    (async () => {
      try {
        const connected = await testApiConnection();
        sendResponse({
          status: 'success',
          connected: connected,
          message: connected ? 'API连接正常' : 'API连接失败'
        });
      } catch (error) {
        sendResponse({
          status: 'error',
          connected: false,
          error: error.message
        });
      }
    })();
    return true; // 异步响应

  } else {
    console.log('❌ Unknown message type:', message.type);
    sendResponse({ status: 'error', message: 'Unknown message type' });
  }
  
  // 返回true表示异步响应
  return true;
});

// 添加安装和启动时的日志
chrome.runtime.onInstalled.addListener(() => {
  console.log('🚀 Extension installed/updated');
  console.log('📅 Installation time:', new Date().toISOString());
  
  // 初始化默认店铺配置
  initializeDefaultStoreConfig();
});

/**
 * 初始化默认店铺配置
 */
async function initializeDefaultStoreConfig() {
  try {
    const storeConfig = await getFromStorage('storeConfig');
    
    if (!storeConfig) {
      const defaultStoreConfig = {
        name: 'Default Store',
        site: 'UK',
        domain: 'sellercentral.amazon.co.uk'
      };
      
      await saveToStorage('storeConfig', defaultStoreConfig);
      console.log('🏪 Default store configuration initialized');
    }
  } catch (error) {
    console.error('❌ Failed to initialize store config:', error);
  }
}

// 添加启动时的日志
console.log('🔄 Background script loaded and ready');
console.log('⏰ Load time:', new Date().toISOString());

// 测试消息传递
setTimeout(() => {
  console.log('🧪 Service Worker test: Background script is running properly');
}, 1000);
