// fileInterceptor.js（运行在页面中）

console.log("📦 fileInterceptor.js injected");

(function () {
  const originalFetch = window.fetch;

  window.fetch = async (...args) => {
    const [resource] = args;
    const url = typeof resource === "string" ? resource : resource.url;

    const response = await originalFetch(...args);
    const clone = response.clone();

    // 监听 defects/pagination 接口（只监听包含statuses=Open参数的）
    if (url.includes('/performance/api/product/policy/defects/pagination') && url.includes('statuses=Open')) {
      console.log("📡 Intercepted request to:", url);

      try {
        const text = await clone.text();
        const json = JSON.parse(text);

        window.postMessage({
          source: 'amazon-api-interceptor',
          type: 'defects-pagination',
          url: url,
          data: json
        }, '*');
      } catch (e) {
        console.warn("❌ Failed to parse JSON response:", e);
      }
    }

    // 监听 summary 接口获取总数据量
    if (url.includes('/performance/api/summary')) {
      console.log("📊 Intercepted summary request to:", url);

      try {
        const text = await clone.text();
        const json = JSON.parse(text);

        // 按照Python脚本逻辑提取总数据量
        const totalNum = json?.listingLevelMetrics?.REGULATORY_COMPLIANCE?.defects?.count || 0;
        console.log("📈 获取总数据量:", totalNum);

        window.postMessage({
          source: 'amazon-api-interceptor',
          type: 'summary',
          url: url,
          data: json,
          totalCount: totalNum
        }, '*');
      } catch (e) {
        console.warn("❌ Failed to parse summary JSON response:", e);
      }
    }

    return response;
  };
})();
