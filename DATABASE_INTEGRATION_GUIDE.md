# Amazon插件数据库集成完成指南

## 🎉 集成完成

您的Amazon插件现在已经完全集成了服务器数据库功能！数据将自动发送到ERP服务器并保存到数据库中。

## 📋 完成的功能

### 1. 服务器API集成
- ✅ 集成ERP API接口 (`https://erpapi.yxyglobal.com`)
- ✅ 自动发送数据到 `data_amazon_compliance` 表
- ✅ 支持批量数据处理（50条/批次）
- ✅ 错误处理和重试机制

### 2. 数据处理流程
- ✅ 数据收集：从Amazon页面收集合规数据
- ✅ 数据缓存：临时存储在内存中
- ✅ 批量发送：自动或手动发送到服务器
- ✅ 本地备份：失败数据保存到本地存储

### 3. 调试和监控
- ✅ 实时状态窗口显示处理进度
- ✅ 调试工具（测试API、发送缓存、查看状态）
- ✅ 详细日志记录
- ✅ 独立API测试页面

## 🚀 使用方法

### 自动模式（推荐）
1. 在Amazon合规页面打开插件
2. 确保URL包含 `script=1` 参数
3. 插件自动运行并发送数据到服务器
4. 查看状态窗口了解处理进度

### 手动控制
在插件状态窗口中使用调试工具：
- **测试API**: 验证服务器连接
- **发送缓存**: 立即发送所有缓存数据
- **缓存状态**: 查看当前缓存数量

## 📊 数据流程

```
Amazon页面数据 → 插件收集 → 内存缓存 → 批量发送 → ERP服务器 → 数据库
                     ↓
                 本地存储备份
```

### 数据字段映射
根据Python爬虫代码，以下字段会被发送到服务器：

| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| unique_id | 唯一标识 | MD5哈希值 |
| platform_account | 店铺名称 | "default_store" |
| platform_site | 站点 | "UK" |
| type | 消息类型 | "产品政策详情" |
| asin | ASIN | "B0ABCD1234" |
| sku | SKU | "MY-SKU-001" |
| status | 状态 | "Open" |
| platform_time | 开始时间 | ********** |
| platform_end_time | 截止时间 | ********** |
| create_time | 创建时间 | ********** |
| brand | 品牌 | "MyBrand" |
| asin_num | ASIN数量 | 1 |
| sku_num | SKU数量 | 1 |
| is_click | 处理状态 | -1/0/1/2 |
| has_product_safety_cert | 风险等级 | 0/1/2 |
| action_type | 操作类型 | "submit_success" |
| submit_result | 提交结果 | JSON字符串 |
| processed_time | 处理时间 | ISO时间格式 |

### 处理状态说明
- `is_click = -1`: 已提交的数据（跳过）
- `is_click = 0`: 未处理的数据
- `is_click = 1`: 提交成功的数据
- `is_click = 2`: 高危无提交按钮（跳过）

## 🔧 测试验证

### 1. 使用API测试页面
```bash
# 在浏览器中打开
file:///path/to/your/project/test_api.html
```

### 2. 检查插件功能
1. 访问Amazon合规页面
2. 在URL后添加 `&script=1`
3. 观察插件状态窗口
4. 使用调试工具测试API连接

### 3. 验证数据库
检查ERP服务器的 `rpa.data_amazon_compliance` 表是否收到数据。

## 📝 日志和监控

### 浏览器控制台
- 打开开发者工具 → Console
- 查看详细的API调用日志
- 监控错误和警告信息

### 插件状态窗口
- 实时显示处理进度
- 统计信息（已处理、已提交、跳过等）
- 运行日志

### 本地存储
在Chrome开发者工具 → Application → Storage → Extension中查看：
- `batch_success_*`: 成功发送的批次
- `batch_failed_*`: 失败发送的批次
- `process_complete_*`: 处理完成统计

## ⚠️ 注意事项

### 1. 网络要求
- 确保能访问 `https://erpapi.yxyglobal.com`
- 网络不稳定时数据会缓存到本地

### 2. 数据安全
- 敏感数据会先保存到本地存储
- 失败的数据不会丢失
- 支持手动重新发送

### 3. 性能优化
- 批量发送减少服务器压力
- 30秒超时自动发送
- 批次间1秒延迟避免过载

## 🐛 故障排除

### API连接失败
1. 检查网络连接
2. 使用测试页面验证API
3. 查看控制台错误信息

### 数据发送失败
1. 检查数据格式
2. 确认必填字段完整
3. 查看服务器响应

### 插件不工作
1. 确认URL包含 `script=1`
2. 检查页面路径是否正确
3. 刷新页面重新加载插件

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 浏览器控制台的错误日志
2. 插件状态窗口的截图
3. 当前访问的Amazon页面URL
4. 网络环境信息

## 🎯 下一步

1. **监控运行**: 观察插件在实际使用中的表现
2. **数据验证**: 定期检查数据库中的数据完整性
3. **性能优化**: 根据使用情况调整批次大小和发送频率
4. **功能扩展**: 根据需要添加更多数据字段或处理逻辑

---

**恭喜！您的Amazon插件现在已经完全集成了数据库功能，可以自动将合规数据发送到ERP服务器！** 🎉
