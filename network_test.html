<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .test-pending {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .test-success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-failed {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔍 网络连接测试工具</h1>
    
    <div class="container">
        <h2>📊 快速测试</h2>
        <button class="button" onclick="runAllTests()">运行所有测试</button>
        <button class="button" onclick="testBasicConnectivity()">基础连接测试</button>
        <button class="button" onclick="testApiEndpoint()">API端点测试</button>
        <button class="button" onclick="clearResults()">清空结果</button>
    </div>

    <div class="container">
        <h2>🧪 测试结果</h2>
        <div id="testResults"></div>
    </div>

    <div class="container">
        <h2>📝 详细日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('log').innerHTML = '';
        }

        function updateTestResult(testId, status, message, details = '') {
            const resultsDiv = document.getElementById('testResults');
            let testDiv = document.getElementById(testId);
            
            if (!testDiv) {
                testDiv = document.createElement('div');
                testDiv.id = testId;
                testDiv.className = 'test-item';
                resultsDiv.appendChild(testDiv);
            }
            
            testDiv.className = `test-item test-${status}`;
            
            let statusIcon = '';
            switch (status) {
                case 'pending': statusIcon = '⏳'; break;
                case 'success': statusIcon = '✅'; break;
                case 'failed': statusIcon = '❌'; break;
            }
            
            testDiv.innerHTML = `
                <strong>${statusIcon} ${message}</strong>
                ${details ? `<div style="margin-top: 5px; font-size: 12px; color: #666;">${details}</div>` : ''}
            `;
        }

        async function testBasicConnectivity() {
            const testId = 'basic-connectivity';
            updateTestResult(testId, 'pending', '基础网络连接测试', '正在测试...');
            log('🔍 开始基础网络连接测试...');
            
            const tests = [
                { name: 'Google', url: 'https://www.google.com' },
                { name: 'Baidu', url: 'https://www.baidu.com' },
                { name: 'GitHub', url: 'https://github.com' }
            ];
            
            let successCount = 0;
            let results = [];
            
            for (let test of tests) {
                try {
                    log(`📡 测试 ${test.name} 连接...`);
                    const startTime = Date.now();
                    
                    const response = await fetch(test.url, {
                        method: 'GET',
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(5000)
                    });
                    
                    const duration = Date.now() - startTime;
                    successCount++;
                    results.push(`${test.name}: 成功 (${duration}ms)`);
                    log(`✅ ${test.name} 连接成功 (${duration}ms)`);
                } catch (error) {
                    results.push(`${test.name}: 失败 - ${error.message}`);
                    log(`❌ ${test.name} 连接失败: ${error.message}`);
                }
            }
            
            const status = successCount > 0 ? 'success' : 'failed';
            const message = `基础网络连接测试 (${successCount}/${tests.length} 成功)`;
            const details = results.join('<br>');
            
            updateTestResult(testId, status, message, details);
            log(`📊 基础网络测试完成: ${successCount}/${tests.length} 成功`);
            
            return successCount > 0;
        }

        async function testApiEndpoint() {
            const testId = 'api-endpoint';
            updateTestResult(testId, 'pending', 'API端点测试', '正在测试...');
            log('🔍 开始API端点测试...');
            
            const baseUrl = 'https://erpapi.yxyglobal.com';
            const apiUrl = `${baseUrl}/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1`;
            
            try {
                // 测试1: 基础服务器连接
                log('📡 测试1: 基础服务器连接...');
                try {
                    const pingResponse = await fetch(baseUrl, {
                        method: 'GET',
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(5000)
                    });
                    log('✅ 基础服务器连接成功');
                } catch (pingError) {
                    log(`⚠️ 基础服务器连接失败: ${pingError.message}`);
                }
                
                // 测试2: API端点连接
                log('📡 测试2: API端点连接...');
                const startTime = Date.now();
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Network-Test-Tool/1.0'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'network_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'network_test_tool'
                        }]
                    }),
                    signal: AbortSignal.timeout(10000)
                });
                
                const duration = Date.now() - startTime;
                log(`📊 API响应状态: ${response.status} ${response.statusText} (${duration}ms)`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                log(`✅ API端点测试成功: ${JSON.stringify(result)}`);
                
                updateTestResult(testId, 'success', 'API端点测试成功', 
                    `响应时间: ${duration}ms<br>状态: ${response.status}<br>结果: ${JSON.stringify(result)}`);
                
                return true;
            } catch (error) {
                log(`❌ API端点测试失败: ${error.message}`);
                
                let errorDetails = `错误: ${error.message}`;
                if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                    errorDetails += '<br>可能原因: CORS限制、网络问题或服务器不可达';
                } else if (error.name === 'AbortError') {
                    errorDetails += '<br>可能原因: 请求超时';
                }
                
                updateTestResult(testId, 'failed', 'API端点测试失败', errorDetails);
                return false;
            }
        }

        async function runAllTests() {
            log('🚀 开始运行所有测试...');
            clearResults();
            
            const basicResult = await testBasicConnectivity();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
            
            const apiResult = await testApiEndpoint();
            
            log('📊 所有测试完成');
            
            // 显示总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-item';
            summaryDiv.style.marginTop = '20px';
            summaryDiv.style.fontWeight = 'bold';
            
            if (basicResult && apiResult) {
                summaryDiv.className += ' test-success';
                summaryDiv.innerHTML = '🎉 所有测试通过！API连接正常。';
            } else if (basicResult && !apiResult) {
                summaryDiv.className += ' test-failed';
                summaryDiv.innerHTML = '⚠️ 基础网络正常，但API连接失败。可能是CORS限制，请在Chrome扩展环境中测试。';
            } else {
                summaryDiv.className += ' test-failed';
                summaryDiv.innerHTML = '❌ 网络连接存在问题，请检查网络设置。';
            }
            
            document.getElementById('testResults').appendChild(summaryDiv);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 网络测试工具已加载');
            log('💡 点击"运行所有测试"开始诊断网络连接问题');
        });
    </script>
</body>
</html>
