<!DOCTYPE html>
<html>
<head>
    <title>Amazon API 测试</title>
</head>
<body>
    <h1>Amazon API 拦截器测试</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="log"></div>
    
    <script>
        function testAPI() {
            console.log('测试API调用');
            fetch('/performance/api/summary')
                .then(response => response.json())
                .then(data => {
                    console.log('API响应:', data);
                    document.getElementById('log').innerHTML += '<p>API调用成功</p>';
                })
                .catch(error => {
                    console.error('API错误:', error);
                    document.getElementById('log').innerHTML += '<p>API调用失败</p>';
                });
        }
    </script>
</body>
</html> 