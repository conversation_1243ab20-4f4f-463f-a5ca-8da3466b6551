// API 客户端模块
import { getFromStorage } from './storage.js';

/**
 * API 配置
 */
const API_CONFIG = {
  BASE_URL: 'https://your-api-server.com/api', // 替换为你的API服务器地址
  TIMEOUT: 10000, // 10秒超时
  RETRY_ATTEMPTS: 3
};

/**
 * 发送数据到API服务器
 * @param {Object} data - 要发送的数据
 * @param {string} endpoint - API端点
 * @returns {Promise<Object>} API响应
 */
export async function sendDataToAPI(data, endpoint = '/amazon-data') {
  try {
    console.log('📤 Sending data to API:', endpoint);
    console.log('📊 Data:', data);
    
    // 获取API配置
    const apiConfig = await getApiConfig();
    
    // 检查API是否启用
    if (!apiConfig.enabled) {
      console.log('⚠️ API sending is disabled');
      return { status: 'disabled' };
    }
    
    // 检查API配置是否有效
    if (!apiConfig.baseUrl || apiConfig.baseUrl === API_CONFIG.BASE_URL) {
      console.warn('⚠️ API base URL not configured, skipping API call');
      return { status: 'no_config' };
    }
    
    const response = await fetch(`${apiConfig.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiConfig.apiKey}`,
        'User-Agent': 'Amazon-Extension/1.0'
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        source: 'amazon-extension',
        data: data
      }),
      signal: AbortSignal.timeout(apiConfig.timeout)
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ API response received:', result);
    
    // 保存API调用记录
    await saveApiCallRecord(endpoint, data, result);
    
    return result;
  } catch (error) {
    console.error('❌ API request failed:', error.message);
    
    // 保存错误记录
    await saveApiErrorRecord(endpoint, data, error);
    
    // 不抛出错误，让调用者决定如何处理
    return { status: 'error', error: error.message };
  }
}

/**
 * 批量发送数据
 * @param {Array} dataArray - 数据数组
 * @param {string} endpoint - API端点
 * @returns {Promise<Array>} 响应数组
 */
export async function sendBatchDataToAPI(dataArray, endpoint = '/amazon-data/batch') {
  try {
    console.log('📦 Sending batch data to API:', dataArray.length, 'items');
    
    const promises = dataArray.map((data, index) => 
      sendDataToAPI(data, endpoint).catch(error => ({
        index,
        error: error.message,
        data
      }))
    );
    
    const results = await Promise.allSettled(promises);
    
    const successful = results.filter(r => r.status === 'fulfilled').map(r => r.value);
    const failed = results.filter(r => r.status === 'rejected').map(r => r.reason);
    
    console.log(`✅ Batch API call completed: ${successful.length} successful, ${failed.length} failed`);
    
    return { successful, failed };
  } catch (error) {
    console.error('❌ Batch API request failed:', error);
    return { successful: [], failed: [{ error: error.message }] };
  }
}

/**
 * 获取API配置
 * @returns {Promise<Object>} API配置
 */
async function getApiConfig() {
  try {
    // 从storage中获取API配置
    const config = await getFromStorage('apiConfig');
    
    return {
      enabled: config?.enabled !== false, // 默认启用
      baseUrl: config?.baseUrl || API_CONFIG.BASE_URL,
      apiKey: config?.apiKey || '',
      timeout: config?.timeout || API_CONFIG.TIMEOUT,
      retryAttempts: config?.retryAttempts || API_CONFIG.RETRY_ATTEMPTS
    };
  } catch (error) {
    console.warn('⚠️ Failed to get API config, using defaults:', error);
    return {
      enabled: true,
      baseUrl: API_CONFIG.BASE_URL,
      apiKey: '',
      timeout: API_CONFIG.TIMEOUT,
      retryAttempts: API_CONFIG.RETRY_ATTEMPTS
    };
  }
}

/**
 * 保存API调用记录
 * @param {string} endpoint - API端点
 * @param {Object} data - 发送的数据
 * @param {Object} response - API响应
 */
async function saveApiCallRecord(endpoint, data, response) {
  try {
    const record = {
      timestamp: new Date().toISOString(),
      endpoint,
      data,
      response,
      status: 'success'
    };
    
    console.log('💾 API call record:', record);
  } catch (error) {
    console.error('❌ Failed to save API call record:', error);
  }
}

/**
 * 保存API错误记录
 * @param {string} endpoint - API端点
 * @param {Object} data - 发送的数据
 * @param {Error} error - 错误信息
 */
async function saveApiErrorRecord(endpoint, data, error) {
  try {
    const record = {
      timestamp: new Date().toISOString(),
      endpoint,
      data,
      error: error.message,
      status: 'error'
    };
    
    console.log('💾 API error record:', record);
  } catch (err) {
    console.error('❌ Failed to save API error record:', err);
  }
}

/**
 * 测试API连接
 * @returns {Promise<boolean>} 连接是否成功
 */
export async function testApiConnection() {
  try {
    const apiConfig = await getApiConfig();
    
    if (!apiConfig.enabled) {
      return false;
    }
    
    if (!apiConfig.baseUrl || apiConfig.baseUrl === API_CONFIG.BASE_URL) {
      return false;
    }
    
    const response = await fetch(`${apiConfig.baseUrl}/health`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiConfig.apiKey}`
      },
      signal: AbortSignal.timeout(5000)
    });
    
    return response.ok;
  } catch (error) {
    console.error('❌ API connection test failed:', error);
    return false;
  }
} 