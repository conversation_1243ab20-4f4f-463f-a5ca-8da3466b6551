// API 客户端模块
import { getFromStorage } from './storage.js';

/**
 * API 配置
 */
const API_CONFIG = {
  BASE_URL: 'https://erpapi.yxyglobal.com', // ERP API服务器地址
  TIMEOUT: 60000, // 60秒超时（适应海外网络）
  RETRY_ATTEMPTS: 3,
  BATCH_SIZE: 50, // 批量处理大小
  TEST_TIMEOUT: 30000, // 测试连接超时（30秒）
  PING_TIMEOUT: 10000 // 基础连接测试超时（10秒）
};

/**
 * 保存Amazon合规数据到服务器
 * @param {Array} dataList - 数据列表
 * @returns {Promise<Object>} API响应
 */
export async function saveAmazonComplianceData(dataList) {
  try {
    if (!Array.isArray(dataList) || dataList.length === 0) {
      console.warn('⚠️ 数据列表为空，跳过保存');
      return { success: true, message: '数据列表为空' };
    }

    const endpoint = '/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1';

    const requestData = {
      table_name: 'data_amazon_compliance',
      unique_key: ['unique_id'],
      validate_fields: ['unique_id', 'platform_account', 'platform_site', 'sku', 'asin'],
      database: 'rpa',
      data: dataList
    };

    console.log('📤 发送Amazon合规数据到服务器:', {
      endpoint,
      dataCount: dataList.length,
      sampleData: dataList[0] // 显示第一条数据作为示例
    });

    console.log(`🌐 发送到海外服务器，超时设置: ${API_CONFIG.TIMEOUT/1000} 秒`);

    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Amazon-Extension/1.0'
      },
      body: JSON.stringify(requestData),
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 服务器响应:', result);

    // 保存API调用记录
    await saveApiCallRecord(endpoint, requestData, result);

    return result;
  } catch (error) {
    console.error('❌ 保存Amazon合规数据失败:', error.message);

    // 保存错误记录
    await saveApiErrorRecord('/amazon-compliance', dataList, error);

    return { success: false, error: error.message };
  }
}

/**
 * 批量保存Amazon合规数据（支持分批处理）
 * @param {Array} dataList - 数据列表
 * @param {number} batchSize - 批次大小，默认50
 * @returns {Promise<Object>} 处理结果
 */
export async function batchSaveAmazonComplianceData(dataList, batchSize = API_CONFIG.BATCH_SIZE) {
  if (!Array.isArray(dataList) || dataList.length === 0) {
    console.warn('⚠️ 数据列表为空，跳过保存');
    return { success: true, message: '数据列表为空' };
  }

  const results = [];
  const totalBatches = Math.ceil(dataList.length / batchSize);

  console.log(`📊 开始批量保存Amazon合规数据，总数: ${dataList.length}，分批数: ${totalBatches}`);

  for (let i = 0; i < dataList.length; i += batchSize) {
    const batch = dataList.slice(i, i + batchSize);
    const batchIndex = Math.floor(i / batchSize) + 1;

    console.log(`📦 处理第 ${batchIndex}/${totalBatches} 批，数量: ${batch.length}`);

    try {
      const result = await saveAmazonComplianceData(batch);
      results.push({
        batchIndex,
        success: result.success !== false,
        result,
        count: batch.length
      });

      // 批次间延迟，避免服务器压力
      if (i + batchSize < dataList.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`❌ 第 ${batchIndex} 批保存失败:`, error);
      results.push({
        batchIndex,
        success: false,
        error: error.message,
        count: batch.length
      });
    }
  }

  const successCount = results.filter(r => r.success).reduce((sum, r) => sum + r.count, 0);
  const failCount = results.filter(r => !r.success).reduce((sum, r) => sum + r.count, 0);

  console.log(`📈 批量保存完成 - 成功: ${successCount}, 失败: ${failCount}`);

  return {
    success: failCount === 0,
    totalCount: dataList.length,
    successCount,
    failCount,
    results
  };
}

/**
 * 发送单条数据到API服务器（兼容旧接口）
 * @param {Object} data - 要发送的数据
 * @param {string} endpoint - API端点
 * @returns {Promise<Object>} API响应
 */
export async function sendDataToAPI(data, endpoint = '/amazon-data') {
  try {
    console.log('� Sending data to API:', endpoint);
    console.log('📊 Data:', data);

    // 如果是Amazon合规数据，使用专门的方法
    if (endpoint.includes('amazon') || data.unique_id) {
      return await saveAmazonComplianceData([data]);
    }

    // 获取API配置
    const apiConfig = await getApiConfig();

    // 检查API是否启用
    if (!apiConfig.enabled) {
      console.log('⚠️ API sending is disabled');
      return { status: 'disabled' };
    }

    const response = await fetch(`${apiConfig.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Amazon-Extension/1.0'
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        source: 'amazon-extension',
        data: data
      }),
      signal: AbortSignal.timeout(apiConfig.timeout)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ API response received:', result);

    // 保存API调用记录
    await saveApiCallRecord(endpoint, data, result);

    return result;
  } catch (error) {
    console.error('❌ API request failed:', error.message);

    // 保存错误记录
    await saveApiErrorRecord(endpoint, data, error);

    // 不抛出错误，让调用者决定如何处理
    return { status: 'error', error: error.message };
  }
}

/**
 * 获取API配置
 * @returns {Promise<Object>} API配置
 */
async function getApiConfig() {
  try {
    // 从storage中获取API配置
    const config = await getFromStorage('apiConfig');

    return {
      enabled: config?.enabled !== false, // 默认启用
      baseUrl: config?.baseUrl || API_CONFIG.BASE_URL,
      apiKey: config?.apiKey || '',
      timeout: config?.timeout || API_CONFIG.TIMEOUT,
      retryAttempts: config?.retryAttempts || API_CONFIG.RETRY_ATTEMPTS
    };
  } catch (error) {
    console.warn('⚠️ Failed to get API config, using defaults:', error);
    return {
      enabled: true,
      baseUrl: API_CONFIG.BASE_URL,
      apiKey: '',
      timeout: API_CONFIG.TIMEOUT,
      retryAttempts: API_CONFIG.RETRY_ATTEMPTS
    };
  }
}

/**
 * 测试API连接
 * @returns {Promise<boolean>} 连接是否成功
 */
export async function testApiConnection() {
  try {
    console.log('🔍 测试API连接...');

    // 首先测试基础连接
    try {
      console.log('📡 步骤1: 测试基础连接...');
      const pingResponse = await fetch(`${API_CONFIG.BASE_URL}/`, {
        method: 'GET',
        signal: AbortSignal.timeout(API_CONFIG.PING_TIMEOUT)
      });
      console.log(`📊 基础连接状态: ${pingResponse.status}`);
    } catch (pingError) {
      console.warn('⚠️ 基础连接失败:', pingError.message);
      // 继续尝试API测试，因为基础连接失败不一定意味着API不可用
    }

    // 测试API端点
    console.log('📡 步骤2: 测试API端点...');
    console.log(`🌐 网络环境: 海外网络，使用 ${API_CONFIG.TEST_TIMEOUT/1000} 秒超时`);

    const response = await fetch(`${API_CONFIG.BASE_URL}/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Amazon-Extension/1.0',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        table_name: 'test_connection',
        unique_key: ['test_id'],
        validate_fields: ['test_id'],
        database: 'rpa',
        data: [{
          test_id: 'connection_test_' + Date.now(),
          test_time: new Date().toISOString(),
          test_source: 'chrome_extension_overseas'
        }]
      }),
      signal: AbortSignal.timeout(API_CONFIG.TEST_TIMEOUT)
    });

    console.log(`📊 API响应状态: ${response.status} ${response.statusText}`);
    console.log(`📊 响应头:`, [...response.headers.entries()]);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ API连接测试成功:', result);
    return true;
  } catch (error) {
    console.error('❌ API连接测试失败:', error);
    console.error('🔍 错误详情:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });

    // 提供更详细的错误信息
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      console.error('💡 可能的原因: 1. 网络连接问题 2. CORS限制 3. 服务器不可达');
    } else if (error.name === 'AbortError') {
      console.error('💡 请求超时，请检查网络连接');
    }

    return false;
  }
}

/**
 * 保存API调用记录
 * @param {string} endpoint - API端点
 * @param {Object} data - 发送的数据
 * @param {Object} response - API响应
 */
async function saveApiCallRecord(endpoint, data, response) {
  try {
    const record = {
      timestamp: new Date().toISOString(),
      endpoint,
      dataCount: Array.isArray(data?.data) ? data.data.length : 1,
      response: response?.success ? 'success' : 'failed',
      status: 'success'
    };

    console.log('💾 API调用记录:', record);
  } catch (error) {
    console.error('❌ Failed to save API call record:', error);
  }
}

/**
 * 保存API错误记录
 * @param {string} endpoint - API端点
 * @param {Object} data - 发送的数据
 * @param {Error} error - 错误信息
 */
async function saveApiErrorRecord(endpoint, data, error) {
  try {
    const record = {
      timestamp: new Date().toISOString(),
      endpoint,
      dataCount: Array.isArray(data) ? data.length : 1,
      error: error.message,
      status: 'error'
    };

    console.log('💾 API错误记录:', record);
  } catch (err) {
    console.error('❌ Failed to save API error record:', err);
  }
}