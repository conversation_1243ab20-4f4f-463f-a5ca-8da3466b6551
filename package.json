{"name": "易信元插件", "version": "1.0.0", "description": "易信元插件", "scripts": {"start": "npm run dev", "dev": "cross-env NODE_ENV=development webpack --watch ", "build": "cross-env NODE_ENV=production  webpack "}, "author": "zengGking", "license": "MIT", "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "@types/chrome": "^0.0.227", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.7.5", "postcss": "^8.4.21", "postcss-loader": "^7.1.0", "postcss-preset-env": "^8.0.1", "sass": "^1.60.0", "sass-loader": "^13.2.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "vue-loader": "^17.0.1", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.14", "webpack": "^5.76.3", "webpack-cli": "^5.0.1"}, "dependencies": {"@vespaiach/axios-fetch-adapter": "^0.3.1", "axios": "^1.3.4", "core-js": "^3.29.1", "vue": "^3.2.47", "vue-router": "^4.2.5"}}