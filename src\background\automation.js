// 自动化操作模块
import { sendDataToAPI } from '../utils/apiClient.js';
import { saveToStorage, getFromStorage } from '../utils/storage.js';

/**
 * 执行自动化操作
 * @param {Object} data - 处理后的Amazon API数据
 */
export async function performAutomationActions(data) {
  try {
    console.log('🤖 Starting automation actions...');
    
    // 1. 保存操作记录（总是执行）
    await saveAutomationRecord(data);
    
    // 2. 发送数据到API（可选，失败不影响其他功能）
    try {
      await sendDataToAPI(data);
      console.log('✅ API data sent successfully');
    } catch (apiError) {
      console.warn('⚠️ API sending failed, but continuing with other operations:', apiError.message);
    }
    
    // 3. 执行页面自动化操作（可选）
    try {
      await performPageAutomation(data);
      console.log('✅ Page automation completed');
    } catch (pageError) {
      console.warn('⚠️ Page automation failed, but continuing:', pageError.message);
    }
    
    console.log('✅ Automation actions completed');
  } catch (error) {
    console.error('❌ Critical automation error:', error);
    // 不抛出错误，避免影响主流程
  }
}

/**
 * 执行页面自动化操作
 * @param {Object} data - 处理后的数据
 */
async function performPageAutomation(data) {
  try {
    // 获取当前活动的Amazon标签页
    const tabs = await chrome.tabs.query({
      url: "*://sellercentral.amazon.co.uk/*",
      active: true
    });
    
    if (tabs.length === 0) {
      console.log('⚠️ No active Amazon seller central tab found');
      return;
    }
    
    const activeTab = tabs[0];
    
    // 注入自动化脚本
    await chrome.scripting.executeScript({
      target: { tabId: activeTab.id },
      function: injectAutomationScript,
      args: [data]
    });
    
    console.log('✅ Page automation script injected');
  } catch (error) {
    console.error('❌ Page automation failed:', error);
    throw error;
  }
}

/**
 * 注入到页面的自动化脚本
 * @param {Object} data - 处理后的数据
 */
function injectAutomationScript(data) {
  console.log('🎯 Automation script running in page context');
  console.log('📊 Data to process:', data);
  
  // 这里可以添加具体的页面操作逻辑
  // 例如：点击按钮、填写表单、导航等
  
  // 示例：查找并点击特定按钮
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    if (button.textContent.includes('Process') || button.textContent.includes('Submit')) {
      console.log('🔘 Found action button:', button.textContent);
      // button.click(); // 取消注释以执行点击
    }
  });
  
  // 示例：填写表单字段
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    if (input.name === 'defectsCount') {
      input.value = data.defectsCount;
      console.log('📝 Filled defects count:', data.defectsCount);
    }
  });
}

/**
 * 保存自动化操作记录
 * @param {Object} data - 处理后的数据
 */
async function saveAutomationRecord(data) {
  try {
    const record = {
      timestamp: new Date().toISOString(),
      action: 'automation_performed',
      data: data,
      status: 'success'
    };
    
    await saveToStorage('automationHistory', record);
    console.log('💾 Automation record saved');
  } catch (error) {
    console.error('❌ Failed to save automation record:', error);
  }
}

/**
 * 获取自动化历史记录
 */
export async function getAutomationHistory() {
  try {
    return await getFromStorage('automationHistory');
  } catch (error) {
    console.error('❌ Failed to get automation history:', error);
    return [];
  }
} 