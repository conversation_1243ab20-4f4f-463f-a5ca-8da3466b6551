<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理检测和配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .proxy-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .test-success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-failed {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔍 代理检测和配置测试工具</h1>
    
    <div class="proxy-info">
        <h3>🌐 当前网络环境</h3>
        <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
        <p><strong>时区:</strong> <span id="timezone"></span></p>
        <p><strong>语言:</strong> <span id="language"></span></p>
        <p><strong>代理状态:</strong> <span id="proxyStatus">检测中...</span></p>
    </div>
    
    <div class="container">
        <h2>🧪 代理检测测试</h2>
        <button class="button" onclick="detectProxy()">检测代理配置</button>
        <button class="button" onclick="testDirectConnection()">测试直连</button>
        <button class="button" onclick="testWithDifferentMethods()">测试不同请求方法</button>
        <div id="proxyResults"></div>
    </div>

    <div class="container">
        <h2>🔧 API连接诊断</h2>
        <button class="button" onclick="diagnoseApiConnection()">完整诊断</button>
        <button class="button" onclick="testXMLHttpRequest()">测试XMLHttpRequest</button>
        <button class="button" onclick="testFetchAPI()">测试Fetch API</button>
        <div id="apiResults"></div>
    </div>

    <div class="container">
        <h2>📝 诊断日志</h2>
        <button class="button" onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${content}</div>`;
        }

        // 初始化页面信息
        function initializePageInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('timezone').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;
            document.getElementById('language').textContent = navigator.language;
        }

        // 检测代理配置
        async function detectProxy() {
            log('🔍 开始检测代理配置...');
            showResult('proxyResults', '正在检测代理配置...', 'info');

            const tests = [
                {
                    name: '国内服务器测试',
                    url: 'https://www.baidu.com',
                    expected: '国内直连'
                },
                {
                    name: '海外服务器测试',
                    url: 'https://www.google.com',
                    expected: '可能通过代理'
                },
                {
                    name: '目标API服务器测试',
                    url: 'https://erpapi.yxyglobal.com',
                    expected: '目标服务器'
                }
            ];

            let results = [];
            for (let test of tests) {
                try {
                    log(`📡 测试 ${test.name}...`);
                    const startTime = Date.now();
                    
                    const response = await fetch(test.url, {
                        method: 'GET',
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(10000)
                    });
                    
                    const duration = Date.now() - startTime;
                    results.push({
                        name: test.name,
                        success: true,
                        duration: duration,
                        status: 'success'
                    });
                    
                    log(`✅ ${test.name} 成功 (${duration}ms)`);
                } catch (error) {
                    results.push({
                        name: test.name,
                        success: false,
                        error: error.message,
                        status: 'failed'
                    });
                    
                    log(`❌ ${test.name} 失败: ${error.message}`);
                }
            }

            // 分析结果
            const baiduSuccess = results.find(r => r.name.includes('国内'))?.success;
            const googleSuccess = results.find(r => r.name.includes('海外'))?.success;
            const apiSuccess = results.find(r => r.name.includes('目标'))?.success;

            let proxyAnalysis = '';
            if (baiduSuccess && !googleSuccess) {
                proxyAnalysis = '🔍 检测结果: 可能在国内网络环境，Google被阻止';
                document.getElementById('proxyStatus').textContent = '国内网络';
            } else if (!baiduSuccess && googleSuccess) {
                proxyAnalysis = '🔍 检测结果: 可能使用海外代理或VPN';
                document.getElementById('proxyStatus').textContent = '海外代理';
            } else if (baiduSuccess && googleSuccess) {
                proxyAnalysis = '🔍 检测结果: 网络环境良好，可能使用智能代理';
                document.getElementById('proxyStatus').textContent = '智能代理';
            } else {
                proxyAnalysis = '🔍 检测结果: 网络连接存在问题';
                document.getElementById('proxyStatus').textContent = '网络异常';
            }

            let resultHtml = '<h4>代理检测结果:</h4><ul>';
            results.forEach(r => {
                if (r.success) {
                    resultHtml += `<li class="test-result test-success">✅ ${r.name}: 成功 (${r.duration}ms)</li>`;
                } else {
                    resultHtml += `<li class="test-result test-failed">❌ ${r.name}: 失败 - ${r.error}</li>`;
                }
            });
            resultHtml += '</ul>';
            resultHtml += `<p><strong>${proxyAnalysis}</strong></p>`;

            showResult('proxyResults', resultHtml, 'info');
            log('🔍 代理检测完成');
        }

        // 测试直连
        async function testDirectConnection() {
            log('🔗 测试直连到API服务器...');
            showResult('proxyResults', '正在测试直连...', 'info');

            try {
                const startTime = Date.now();
                const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table_name: 'test_connection',
                        unique_key: ['test_id'],
                        validate_fields: ['test_id'],
                        database: 'rpa',
                        data: [{
                            test_id: 'direct_test_' + Date.now(),
                            test_time: new Date().toISOString(),
                            test_source: 'direct_connection_test'
                        }]
                    }),
                    signal: AbortSignal.timeout(30000)
                });

                const duration = Date.now() - startTime;
                const result = await response.json();
                
                log(`✅ 直连测试成功 (${duration}ms): ${JSON.stringify(result)}`);
                showResult('proxyResults', `直连测试成功！<br>响应时间: ${duration}ms<br>状态: ${response.status}<br>结果: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ 直连测试失败: ${error.message}`);
                showResult('proxyResults', `直连测试失败: ${error.message}`, 'error');
            }
        }

        // 测试不同请求方法
        async function testWithDifferentMethods() {
            log('🧪 测试不同的请求方法...');
            showResult('proxyResults', '正在测试不同请求方法...', 'info');

            const methods = [
                {
                    name: 'Fetch API (标准)',
                    test: () => testWithFetch()
                },
                {
                    name: 'XMLHttpRequest',
                    test: () => testWithXHR()
                },
                {
                    name: 'Fetch with no-cors',
                    test: () => testWithFetchNoCors()
                }
            ];

            let results = [];
            for (let method of methods) {
                try {
                    log(`📡 测试 ${method.name}...`);
                    const result = await method.test();
                    results.push({
                        name: method.name,
                        success: true,
                        result: result
                    });
                    log(`✅ ${method.name} 成功`);
                } catch (error) {
                    results.push({
                        name: method.name,
                        success: false,
                        error: error.message
                    });
                    log(`❌ ${method.name} 失败: ${error.message}`);
                }
            }

            let resultHtml = '<h4>不同请求方法测试结果:</h4><ul>';
            results.forEach(r => {
                if (r.success) {
                    resultHtml += `<li class="test-result test-success">✅ ${r.name}: 成功</li>`;
                } else {
                    resultHtml += `<li class="test-result test-failed">❌ ${r.name}: 失败 - ${r.error}</li>`;
                }
            });
            resultHtml += '</ul>';

            const successCount = results.filter(r => r.success).length;
            if (successCount > 0) {
                resultHtml += `<p><strong>建议: 使用成功的方法进行API调用</strong></p>`;
            } else {
                resultHtml += `<p><strong>所有方法都失败，可能是网络配置问题</strong></p>`;
            }

            showResult('proxyResults', resultHtml, successCount > 0 ? 'success' : 'error');
            log('🧪 不同请求方法测试完成');
        }

        // 使用Fetch API测试
        async function testWithFetch() {
            const response = await fetch('https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table_name: 'test_connection',
                    unique_key: ['test_id'],
                    validate_fields: ['test_id'],
                    database: 'rpa',
                    data: [{
                        test_id: 'fetch_test_' + Date.now(),
                        test_time: new Date().toISOString()
                    }]
                }),
                signal: AbortSignal.timeout(30000)
            });
            return await response.json();
        }

        // 使用XMLHttpRequest测试
        async function testWithXHR() {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'https://erpapi.yxyglobal.com/api/v1/index/post?c=rpa/data&a=saveData&not_check_md5=1');
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.timeout = 30000;
                
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        resolve(JSON.parse(xhr.responseText));
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.ontimeout = function() {
                    reject(new Error('Request timeout'));
                };
                
                xhr.send(JSON.stringify({
                    table_name: 'test_connection',
                    unique_key: ['test_id'],
                    validate_fields: ['test_id'],
                    database: 'rpa',
                    data: [{
                        test_id: 'xhr_test_' + Date.now(),
                        test_time: new Date().toISOString()
                    }]
                }));
            });
        }

        // 使用Fetch no-cors测试
        async function testWithFetchNoCors() {
            const response = await fetch('https://erpapi.yxyglobal.com/', {
                method: 'GET',
                mode: 'no-cors',
                signal: AbortSignal.timeout(10000)
            });
            return { status: 'no-cors mode success' };
        }

        // 完整诊断
        async function diagnoseApiConnection() {
            log('🔍 开始完整API连接诊断...');
            showResult('apiResults', '正在进行完整诊断...', 'info');

            await detectProxy();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testDirectConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testWithDifferentMethods();

            log('🔍 完整诊断完成');
            showResult('apiResults', '完整诊断已完成，请查看上方结果', 'success');
        }

        // 测试XMLHttpRequest
        async function testXMLHttpRequest() {
            log('🧪 单独测试XMLHttpRequest...');
            try {
                const result = await testWithXHR();
                log(`✅ XMLHttpRequest测试成功: ${JSON.stringify(result)}`);
                showResult('apiResults', `XMLHttpRequest测试成功: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ XMLHttpRequest测试失败: ${error.message}`);
                showResult('apiResults', `XMLHttpRequest测试失败: ${error.message}`, 'error');
            }
        }

        // 测试Fetch API
        async function testFetchAPI() {
            log('🧪 单独测试Fetch API...');
            try {
                const result = await testWithFetch();
                log(`✅ Fetch API测试成功: ${JSON.stringify(result)}`);
                showResult('apiResults', `Fetch API测试成功: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`❌ Fetch API测试失败: ${error.message}`);
                showResult('apiResults', `Fetch API测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 代理检测和配置测试工具已加载');
            initializePageInfo();
            
            // 自动开始检测
            setTimeout(() => {
                detectProxy();
            }, 1000);
        });
    </script>
</body>
</html>
